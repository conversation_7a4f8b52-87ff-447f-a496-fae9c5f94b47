import re
"""
Vertex AI service - Versión corregida y robustecida
Basado en los resultados de debug y análisis del problema con thought_signature y mensajes duplicados.
"""

import logging
import json
import time
import asyncio
from typing import List, Any, Optional
from fastapi import HTTPException, status
import traceback

# Vertex AI imports
import vertexai
from vertexai.generative_models import GenerativeModel, Content, Part
import vertexai.generative_models as generative_models

from app.config import settings

logger = logging.getLogger(__name__)

def call_vertex_ai_with_timeout(model, contents_history, timeout_seconds=90):
    """
    Llama a Vertex AI con timeout para evitar esperas indefinidas.

    Args:
        model: GenerativeModel instance
        contents_history: Lista de contenidos para enviar
        timeout_seconds: Timeout en segundos (default: 90)

    Returns:
        Response de Vertex AI

    Raises:
        TimeoutError: Si la llamada tarda más del timeout
        Exception: Otros errores de Vertex AI
    """
    import concurrent.futures

    def make_call():
        return model.generate_content(contents_history)

    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(make_call)
        try:
            return future.result(timeout=timeout_seconds)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"Vertex AI call timed out after {timeout_seconds} seconds")

async def initialize_gemini_model_with_market_data() -> Optional[GenerativeModel]:
    """
    Initialize Gemini model with market data context (NO TOOLS)

    Returns:
        GenerativeModel instance or None if initialization fails
    """
    try:
        print(f"🔧 Initializing Vertex AI...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")
        
        # Setup Google Cloud credentials from JSON string
        if settings.google_credentials_json:
            from google.oauth2 import service_account

            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info:
                    print("🔐 Replacing literal \n with actual newlines in private_key")
                    credentials_info['private_key'] = re.sub(r'\\n', '\n', credentials_info['private_key'])
            except json.JSONDecodeError as e:
                print(f"🔐 JSON parse failed, trying with newline replacement: {e}")
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            print(f"🔐 Credentials project_id: {credentials_info.get('project_id')}")
            # Added for debugging
            print(f"🔐 Credentials info keys: {credentials_info.keys()}")
            if 'private_key' in credentials_info:
                print("🔐 Private key found.")
                print(f"🔐 Private key starts with: {credentials_info['private_key'][:30]}")
            else:
                print("🔐 Private key not found.")

            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            print("⚠️ No Google credentials JSON found, using default authentication")
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )
        
        # Esta función NO usa herramientas

        try:
            print(f"🔧 Creating GenerativeModel WITHOUT tools...")

            # Configurar parámetros de generación
            generation_config = {
                "max_output_tokens": 8192,
                "temperature": 0.7,
                "top_p": 0.95,
            }

            model = GenerativeModel(
                model_name="gemini-2.5-pro",
                tools=None,  # SIN HERRAMIENTAS
                generation_config=generation_config,
                system_instruction="""
Eres un asistente financiero experto especializado en análisis técnico avanzado. Responde SIEMPRE con texto claro y visible en español.

NUEVAS CAPACIDADES AVANZADAS:
- Análisis multi-timeframe para confirmar tendencias
- Reconocimiento de patrones gráficos clásicos
- Análisis de volumen para confirmar movimientos
- Identificación de soportes y resistencias clave
- Evaluación de confluencias técnicas

HERRAMIENTAS DISPONIBLES:
1. get_price_data - Datos históricos OHLCV
2. apply_indicator - Indicadores técnicos (RSI, MACD, SMA, EMA, BBANDS, STOCH, ATR, ADX, OBV, MFI, CCI, WILLIAMS_R, MOMENTUM, KELTNER, DONCHIAN)
3. get_multi_timeframe_analysis - Análisis en múltiples marcos temporales
4. detect_chart_patterns - Reconocimiento de patrones gráficos
5. calculate_vwap - Volume Weighted Average Price
6. analyze_volume_confirmation - Confirmación por volumen

DIRECTRICES PARA ANÁLISIS MEJORADO:
1. Siempre considera el volumen al analizar movimientos de precio
2. Utiliza múltiples marcos temporales para contexto
3. Identifica patrones gráficos cuando sea relevante
4. Menciona niveles de soporte/resistencia importantes
5. Proporciona confluencias técnicas cuando existan

Incluye siempre: "Esta información es solo para fines educativos."

IMPORTANTE: Tu respuesta debe ser texto legible y útil con análisis completo."""
            )

            print(f"✅ Gemini model initialized successfully WITHOUT tools")
            logger.info(f"Gemini model initialized successfully WITHOUT tools")
            return model

        except Exception as e:
            logger.error(f"Error creating GenerativeModel: {e}")
            print(f"❌ Error creating GenerativeModel: {e}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return None
        
    except Exception as e:
        logger.error(f"Error initializing Gemini model: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None

def initialize_gemini_model(tools: List[Any]) -> Optional[GenerativeModel]:
    """
    FUNCIÓN ORIGINAL - mantener para compatibilidad
    """
    # Llamar a la nueva función sin herramientas
    import asyncio
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(initialize_gemini_model_with_market_data())
    except:
        # Si no hay loop, crear uno nuevo
        return asyncio.run(initialize_gemini_model_with_market_data())

async def initialize_gemini_model_no_tools() -> Optional[GenerativeModel]:
    """
    Initialize Gemini model WITHOUT tools, using market data from database
    """
    try:
        print(f"🔧 Initializing Vertex AI WITHOUT tools...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")

        # Setup Google Cloud credentials
        if settings.google_credentials_json:
            from google.oauth2 import service_account
            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info:
                    credentials_info['private_key'] = credentials_info['private_key'].replace('\\n', '\n')
            except json.JSONDecodeError as e:
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )

        # Obtener contexto de mercado de la base de datos
        print("📊 Obteniendo datos de mercado de la base de datos...")
        from app.services.market_data_service import market_data_service
        market_context = await market_data_service.get_market_context_for_ai()
        print(f"✅ Contexto de mercado obtenido ({len(market_context)} caracteres)")

        # Configurar parámetros de generación optimizados
        generation_config = {
            "max_output_tokens": 8192,
            "temperature": 0.7,
            "top_p": 0.95,
        }

        # System instruction con datos de mercado incluidos
        system_instruction = f"""Eres un asistente financiero experto especializado en análisis técnico. Responde SIEMPRE con texto claro y visible en español.

{market_context}

INDICADORES TÉCNICOS DISPONIBLES:
Tienes acceso completo a los siguientes indicadores técnicos para los activos principales:

• RSI (Relative Strength Index): Mide sobrecompra (>70) y sobreventa (<30)
• MACD (Moving Average Convergence Divergence): Indica momentum alcista (>0) o bajista (<0)
• STOCH (Stochastic Oscillator): Sobrecompra (>80), sobreventa (<20)
• ATR (Average True Range): Mide volatilidad (alta >5, moderada 2-5, baja <2)
• ADX (Average Directional Index): Fuerza de tendencia (fuerte >25, débil <25)
• SMA (Simple Moving Average): Media móvil para identificar tendencias
• EMA (Exponential Moving Average): Media móvil exponencial más sensible
• BBANDS (Bollinger Bands): Bandas de volatilidad

CAPACIDADES DE ANÁLISIS:
- Análisis técnico completo usando múltiples indicadores
- Identificación de señales de compra/venta
- Evaluación de momentum y tendencias
- Análisis de volatilidad y riesgo
- Comparación entre activos y sectores
- Detección de divergencias y patrones

INSTRUCCIONES:
- Usa TODOS los indicadores disponibles para análisis completos
- Combina múltiples indicadores para señales más robustas
- Explica las razones técnicas detrás de tus análisis
- Proporciona contexto educativo sobre los indicadores
- Siempre incluye: "Esta información es solo para fines educativos"
- Responde de manera natural y conversacional

IMPORTANTE: Tienes acceso a datos completos de indicadores técnicos, úsalos para análisis profundos y precisos."""

        model = GenerativeModel(
            model_name="gemini-2.5-pro",
            tools=None,  # SIN HERRAMIENTAS
            generation_config=generation_config,
            system_instruction=system_instruction
        )

        print(f"✅ Gemini model initialized successfully WITHOUT tools")
        logger.info(f"Gemini model initialized with market data context")
        return model

    except Exception as e:
        logger.error(f"Error initializing Gemini model without tools: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None


async def initialize_gemini_model_with_dynamic_tools() -> Optional[GenerativeModel]:
    """
    Initialize Gemini model WITH Supabase tools for dynamic data access
    """
    try:
        print(f"🔧 Initializing Vertex AI WITH dynamic tools...")
        print(f"🔧 Project: {settings.vertex_ai_project}")
        print(f"🔧 Location: {settings.vertex_ai_location}")

        # Setup Google Cloud credentials
        if settings.google_credentials_json:
            from google.oauth2 import service_account
            print("🔐 Setting up Google Cloud credentials from JSON...")

            try:
                credentials_info = json.loads(settings.google_credentials_json)
                if 'private_key' in credentials_info:
                    credentials_info['private_key'] = credentials_info['private_key'].replace('\\n', '\n')
            except json.JSONDecodeError as e:
                credentials_json = settings.google_credentials_json.replace('\\n', '\n')
                credentials_info = json.loads(credentials_json)

            credentials = service_account.Credentials.from_service_account_info(
                credentials_info,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )

            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location,
                credentials=credentials
            )
            print("✅ Vertex AI initialized with service account credentials")
        else:
            vertexai.init(
                project=settings.vertex_ai_project,
                location=settings.vertex_ai_location
            )

        # Obtener herramientas de Supabase Y TradingView
        print("🔧 Loading Supabase tools...")
        from app.tools.supabase_tools import get_supabase_tools
        supabase_tools = get_supabase_tools()
        print(f"✅ Loaded {len(supabase_tools)} Supabase tools")

        # Añadir herramientas de TradingView para datos en tiempo real
        print("🔧 Adding TradingView tools...")
        tradingview_tools = [
            {
                "name": "get_price_data",
                "description": "Obtiene datos históricos de precios OHLCV en tiempo real para cualquier activo financiero (acciones, criptomonedas, forex, etc.)",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {
                            "type": "string",
                            "description": "Símbolo del activo con exchange (ej: 'NASDAQ:TSLA', 'BINANCE:BTCUSDT', 'NASDAQ:AAPL')"
                        },
                        "interval": {
                            "type": "string",
                            "description": "Intervalo de tiempo ('1m', '5m', '15m', '30m', '1h', '4h', '1D', '1W', '1M')",
                            "default": "1D"
                        },
                        "n_bars": {
                            "type": "integer",
                            "description": "Número de barras a obtener (máximo 5000)",
                            "default": 100
                        }
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "apply_indicator",
                "description": "Calcula indicadores técnicos (RSI, MACD, SMA, EMA, Bollinger Bands, etc.) para un activo específico",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {
                            "type": "string",
                            "description": "Símbolo del activo con exchange"
                        },
                        "interval": {
                            "type": "string",
                            "description": "Intervalo de tiempo",
                            "default": "1D"
                        },
                        "indicator_name": {
                            "type": "string",
                            "description": "Nombre del indicador (RSI, MACD, SMA, EMA, BBANDS, STOCH, ATR, ADX)"
                        },
                        "parameters": {
                            "type": "object",
                            "description": "Parámetros del indicador (ej: {'length': 14} para RSI)"
                        }
                    },
                    "required": ["symbol", "indicator_name"]
                }
            }
        ]

        # Combinar todas las herramientas
        all_tools = supabase_tools + tradingview_tools
        print(f"✅ Total tools available: {len(all_tools)} ({len(supabase_tools)} Supabase + {len(tradingview_tools)} TradingView)")

        # Convertir herramientas a formato Vertex AI
        vertex_tools = []
        if tools:
            print(f"🔧 Converting {len(tools)} tools to Vertex AI format...")
            function_declarations = []
            for i, tool in enumerate(tools):
                try:
                    print(f"🔧 Processing tool {i+1}: {tool.get('name', 'unknown')}")
                    if isinstance(tool, dict):
                        func_decl = generative_models.FunctionDeclaration(
                            name=tool["name"],
                            description=tool["description"],
                            parameters=tool["parameters"]
                        )
                        function_declarations.append(func_decl)
                        print(f"✅ Successfully converted tool: {tool['name']}")
                except Exception as e:
                    logger.error(f"Error converting tool {tool}: {e}")
                    print(f"❌ Error converting tool {i+1}: {e}")

            if function_declarations:
                try:
                    combined_tool = generative_models.Tool(function_declarations=function_declarations)
                    vertex_tools.append(combined_tool)
                    print(f"✅ Combined tool created with {len(function_declarations)} functions")
                except Exception as e:
                    logger.error(f"Error creating combined tool: {e}")
                    print(f"❌ Error creating combined tool: {e}")

        # Configurar parámetros de generación optimizados para function calling
        generation_config = {
            "max_output_tokens": 8192,
            "temperature": 0.1,  # Reducir temperatura para más determinismo
            "top_p": 0.8,        # Reducir top_p para más precisión
            "top_k": 20,         # Agregar top_k para mejor control
        }

        # System instruction ultra-específico para forzar function calling
        system_instruction = """Eres un asistente financiero. IMPORTANTE: SIEMPRE debes usar las herramientas disponibles.

INSTRUCCIONES CRÍTICAS:
1. Para CUALQUIER pregunta sobre activos financieros, DEBES llamar a get_asset_data_from_db
2. Para búsquedas de activos, DEBES llamar a find_assets_by_criteria
3. Para resúmenes de mercado, DEBES llamar a get_market_summary
4. NUNCA respondas sin usar una herramienta primero

EJEMPLOS EXACTOS:
- Usuario: "¿Precio de Tesla?" → TÚ: Llama get_asset_data_from_db(symbol="NASDAQ:TSLA")
- Usuario: "¿Precio de Apple?" → TÚ: Llama get_asset_data_from_db(symbol="NASDAQ:AAPL")
- Usuario: "Activos baratos" → TÚ: Llama find_assets_by_criteria(criteria="oversold")
- Usuario: "¿Cómo está el mercado?" → TÚ: Llama get_market_summary(category="all")

REGLA ABSOLUTA: NO escribas texto sin llamar herramientas. SIEMPRE usa las funciones disponibles."""

        # Configurar tool config para forzar el uso de herramientas
        from vertexai.generative_models import ToolConfig

        tool_config = ToolConfig(
            function_calling_config=ToolConfig.FunctionCallingConfig(
                mode=ToolConfig.FunctionCallingConfig.Mode.ANY  # Forzar uso de herramientas
            )
        ) if vertex_tools else None

        model = GenerativeModel(
            model_name="gemini-2.5-pro",  # Usar 2.0 Flash para parallel function calling
            tools=vertex_tools if vertex_tools else None,
            tool_config=tool_config,
            generation_config=generation_config,
            system_instruction=system_instruction
        )

        print(f"✅ Gemini model initialized successfully WITH {len(vertex_tools)} dynamic tools")
        logger.info(f"Gemini model initialized with dynamic Supabase tools")
        return model

    except Exception as e:
        logger.error(f"Error initializing Gemini model with dynamic tools: {e}")
        print(f"❌ Error initializing Gemini model: {e}")
        return None

async def generate_chat_response(
    contents_history: List[Content],
    model: GenerativeModel
) -> Any:
    """
    Genera una respuesta del modelo Gemini.
    Devuelve el objeto de respuesta completo para un manejo adecuado en el orquestador.
    """
    if not model:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="AI model is not available."
        )

    try:
        # Forzar el uso de herramientas es una buena práctica para que el modelo no intente adivinar
        from vertexai.generative_models import ToolConfig, GenerationConfig

        tool_config = ToolConfig(
            function_calling_config=ToolConfig.FunctionCallingConfig(
                mode=ToolConfig.FunctionCallingConfig.Mode.ANY
            )
        )

        response = model.generate_content(
            contents_history,
            tool_config=tool_config,
            generation_config=GenerationConfig(temperature=0)
        )

        return response

    except Exception as e:
        logger.error(f"Exception in generate_chat_response: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate AI response: {e}"
        )


