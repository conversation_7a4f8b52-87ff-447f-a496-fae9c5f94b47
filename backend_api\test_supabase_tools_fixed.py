#!/usr/bin/env python3
"""
Test específico para verificar que las herramientas de Supabase funcionan correctamente
después de las correcciones implementadas.
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime
from app.routes.chat import _process_chat_request_with_function_calling
from app.models.chat import ChatRequest, ChatMessage

# Configurar logging para ver el flujo detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_supabase_tools_corrected():
    """Test de las herramientas de Supabase con las correcciones implementadas."""
    print("🔧 TESTING CORRECTED SUPABASE TOOLS FLOW")
    print("=" * 60)
    
    # Usar UUID válido para evitar errores de base de datos
    test_user_id = str(uuid.uuid4())
    mock_user = {
        "id": test_user_id,
        "email": "<EMAIL>"
    }
    
    # Casos de prueba específicos para herramientas de Supabase
    test_cases = [
        {
            "name": "Consulta datos de Tesla desde Supabase",
            "message": "Muéstrame todos los datos disponibles de Tesla (TSLA)",
            "expected_tool": "get_asset_data_from_db",
            "expected_symbol": "NASDAQ:TSLA"
        },
        {
            "name": "Análisis técnico completo de Apple",
            "message": "Dame el análisis técnico completo de Apple",
            "expected_tool": "get_technical_analysis_summary",
            "expected_symbol": "NASDAQ:AAPL"
        },
        {
            "name": "Buscar activos en sobreventa",
            "message": "Encuentra activos que estén en zona de sobreventa (RSI < 30)",
            "expected_tool": "find_assets_by_criteria",
            "expected_criteria": "oversold"
        },
        {
            "name": "Resumen general del mercado",
            "message": "¿Cómo está el mercado en general?",
            "expected_tool": "get_market_summary",
            "expected_category": "all"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 TEST CASE {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            # Crear request de chat
            request = ChatRequest(
                message=test_case["message"],
                history=[]
            )
            
            print(f"💬 Usuario: {test_case['message']}")
            print(f"🎯 Herramienta esperada: {test_case['expected_tool']}")
            
            # Procesar con el flujo corregido
            start_time = datetime.now()
            response = await _process_chat_request_with_function_calling(request, mock_user)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            print(f"⏱️  Tiempo de procesamiento: {processing_time:.2f} segundos")
            print(f"🤖 Respuesta de la IA:")
            print(f"   Longitud: {len(response.reply)} caracteres")
            print(f"   Preview: {response.reply[:150]}...")
            
            # Análisis detallado de la respuesta
            response_lower = response.reply.lower()
            
            # Verificar si se usaron herramientas (indicadores en la respuesta)
            tool_usage_indicators = {
                "Menciona datos específicos": any(word in response_lower for word in ['precio', 'rsi', 'macd', 'volumen', 'datos']),
                "Incluye números/valores": any(char.isdigit() for char in response.reply),
                "Menciona símbolo correcto": any(symbol in response_lower for symbol in ['tesla', 'tsla', 'apple', 'aapl', 'nasdaq']),
                "No es respuesta genérica": "lo siento" not in response_lower and "no pude procesar" not in response_lower,
                "Incluye descargo responsabilidad": "educativo" in response_lower or "asesoramiento" in response_lower,
                "Respuesta sustancial": len(response.reply) > 100
            }
            
            print(f"📊 Análisis de uso de herramientas:")
            for indicator, passed in tool_usage_indicators.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {indicator}")
            
            # Calcular score de éxito
            success_score = sum(tool_usage_indicators.values()) / len(tool_usage_indicators) * 100
            print(f"🎯 Score de éxito: {success_score:.1f}%")
            
            if success_score >= 80:
                print("🎉 EXCELENTE: Herramientas funcionando correctamente")
            elif success_score >= 60:
                print("✅ BUENO: Herramientas funcionando aceptablemente")
            elif success_score >= 40:
                print("⚠️  REGULAR: Herramientas funcionando parcialmente")
            else:
                print("❌ POBRE: Herramientas no funcionando correctamente")
                
        except Exception as e:
            print(f"❌ Error en test case {i}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 60)
    print("🎯 RESUMEN DE CORRECCIONES IMPLEMENTADAS:")
    print("✅ Eliminadas herramientas de TradingView (solo Supabase)")
    print("✅ Cambiado ToolConfig de ANY a AUTO")
    print("✅ Mejoradas instrucciones del sistema")
    print("✅ Añadida instrucción específica después de thought_signature")
    print("✅ Usado UUID válido para evitar errores de BD")
    print("✅ Logging detallado para debugging")

async def test_direct_tool_execution():
    """Test directo de las herramientas de Supabase."""
    print(f"\n🔧 TESTING DIRECT SUPABASE TOOL EXECUTION")
    print("-" * 50)
    
    try:
        from app.tools.supabase_tools import get_asset_data_from_db, get_supabase_tools
        
        # Verificar herramientas disponibles
        tools = get_supabase_tools()
        print(f"✅ Herramientas disponibles: {len(tools)}")
        for tool in tools:
            print(f"   - {tool['name']}: {tool['description'][:50]}...")
        
        # Test directo de herramienta
        print(f"\n🧪 Probando get_asset_data_from_db directamente...")
        result = await get_asset_data_from_db("NASDAQ:TSLA")
        
        print(f"📊 Resultado:")
        print(f"   Símbolo: {result.get('symbol', 'N/A')}")
        print(f"   Fuentes de datos: {len(result.get('data_sources', []))}")
        print(f"   Tiene error: {'error' in result}")
        
        if 'error' not in result:
            print("✅ Herramienta ejecutada exitosamente")
        else:
            print(f"⚠️  Herramienta devolvió error: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error en test directo: {e}")

async def main():
    """Ejecutar todas las pruebas."""
    await test_supabase_tools_corrected()
    await test_direct_tool_execution()

if __name__ == "__main__":
    asyncio.run(main())
