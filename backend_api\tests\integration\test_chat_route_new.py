"""
Integration tests for chat route with function calling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from datetime import datetime

from app.main import app


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock user data."""
    return {
        "id": "test-user-id-12345",
        "email": "<EMAIL>",
        "created_at": "2025-01-01T00:00:00Z",
        "email_confirmed_at": "2025-01-01T00:00:00Z",
        "app_metadata": {},
        "user_metadata": {}
    }


class TestChatEndpointWithFunctionCalling:
    """Test the new chat flow with function calling."""
    
    @patch('app.services.supabase_client.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_with_single_tool_call(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow: question → tool call → final response."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # First call: AI requests tool
        # Second call: AI provides final response
        mock_generate_response.side_effect = [
            {
                "type": "function_call",
                "function_name": "get_asset_data_from_db",
                "function_args": {"symbol": "NASDAQ:TSLA"}
            },
            {
                "type": "text",
                "content": "Basándome en los datos actuales de Tesla (NASDAQ:TSLA), el precio actual es de $250.00 con un RSI de 65, indicando una condición neutral. Esta información es solo para fines educativos."
            }
        ]
        
        # Mock tool execution
        mock_execute_tool.return_value = {
            "symbol": "NASDAQ:TSLA",
            "market_data": {"current_price": 250.0},
            "technical_indicators": {"RSI": {"value": 65.0, "interpretation": "neutral"}}
        }
        
        mock_save_history.return_value = None
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "¿Cuál es el precio actual de Tesla?", "history": []},
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "Tesla" in response_data["reply"]
        assert "250" in response_data["reply"]
        assert "educativos" in response_data["reply"]
        
        # Verify function calls
        assert mock_generate_response.call_count == 2
        mock_execute_tool.assert_called_once_with("get_asset_data_from_db", {"symbol": "NASDAQ:TSLA"})
        mock_save_history.assert_called_once()
    
    @patch('app.services.supabase_client.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_with_tool_error(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow when tool execution fails."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # AI requests tool, then provides response based on error
        mock_generate_response.side_effect = [
            {
                "type": "function_call",
                "function_name": "get_asset_data_from_db",
                "function_args": {"symbol": "INVALID:SYMBOL"}
            },
            {
                "type": "text",
                "content": "Lo siento, no pude obtener los datos para ese símbolo. Por favor verifica que el símbolo sea correcto. Esta información es solo para fines educativos."
            }
        ]
        
        # Mock tool execution error
        mock_execute_tool.return_value = {
            "error": "Error executing get_asset_data_from_db: Symbol not found"
        }
        
        mock_save_history.return_value = None
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "¿Cuál es el precio de INVALID:SYMBOL?", "history": []},
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "Lo siento" in response_data["reply"]
        assert "educativos" in response_data["reply"]
        
        # Verify function calls
        assert mock_generate_response.call_count == 2
        mock_execute_tool.assert_called_once()
        mock_save_history.assert_called_once()


class TestChatAuthentication:
    """Test authentication for chat endpoints."""
    
    def test_chat_unauthenticated(self, client):
        """Test that chat endpoint requires authentication."""
        response = client.post("/api/v1/chat/", json={"message": "test", "history": []})
        # Should return 422 for missing Authorization header
        assert response.status_code in [401, 422]
    
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    def test_chat_invalid_token(self, mock_validate, client):
        """Test that chat endpoint returns 401 with invalid token."""
        from fastapi import HTTPException, status
        
        mock_validate.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
        
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "test", "history": []},
            headers=headers
        )
        
        assert response.status_code == 401


class TestChatHealthEndpoint:
    """Test the chat health endpoint."""
    
    def test_chat_health(self, client):
        """Test that chat health endpoint works."""
        response = client.get("/api/v1/chat/health")
        
        assert response.status_code == 200
        
        response_data = response.json()
        assert response_data["status"] == "healthy"
        assert response_data["service"] == "chat"
        assert "timestamp" in response_data
