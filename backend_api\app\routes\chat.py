"""
Chat routes for TradingIA Backend.

This module defines the main chat endpoint that orchestrates
authentication, AI interaction, and tool execution.
"""

from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.chat import Chat<PERSON><PERSON><PERSON>, ChatResponse
from app.services.supabase_client import validate_user_token, save_chat_history
from app.services.vertex_ai import initialize_gemini_model, generate_chat_response, initialize_gemini_model_with_dynamic_tools
from app.tools.tradingview_provider import get_price_data, apply_indicator
from app.services.market_data_service import market_data_service
from app.services.market_data_service import market_data_service
from typing import Dict, Any, List
import logging
import json
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/chat", tags=["chat"])

# Security scheme
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency to validate user authentication.
    
    This function extracts and validates the JWT token from the
    Authorization header and returns user information.
    
    Args:
        credentials: HTTP Bearer credentials from the request header
        
    Returns:
        Dict[str, Any]: User information if token is valid
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Validate the token using Supabase
        user_info = await validate_user_token(credentials.credentials)
        return user_info
        
    except HTTPException:
        # Re-raise authentication errors
        raise
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        )


@router.post("/debug", response_model=ChatResponse)
async def chat_debug_endpoint(request: ChatRequest) -> ChatResponse:
    """
    DEBUG endpoint for testing without authentication.
    ONLY FOR DEVELOPMENT - REMOVE IN PRODUCTION
    """
    print("🔧 === DEBUG ENDPOINT CALLED ===")
    print(f"🔧 Message: {request.message}")
    print(f"🔧 History: {len(request.history)} messages")

    # Create a fake user for testing
    fake_user = {
        "id": "debug-user-12345",
        "email": "<EMAIL>"
    }

    return await _process_chat_request(request, fake_user)

@router.post("/", response_model=ChatResponse)
async def chat_endpoint(
    request: ChatRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> ChatResponse:
    """
    Main chat endpoint for AI interactions.

    This endpoint handles user chat requests, validates authentication,
    processes the request through the AI model, executes any required
    tools, and returns the final response.

    Args:
        request: Chat request containing message and history
        current_user: Authenticated user information

    Returns:
        ChatResponse: AI-generated response

    Raises:
        HTTPException: If processing fails
    """
    return await _process_chat_request_with_function_calling(request, current_user)


def prepare_conversation_history(request: ChatRequest) -> List[Dict[str, str]]:
    """
    Prepare conversation history for the AI model.

    Args:
        request: Chat request containing message and history

    Returns:
        List of conversation messages in the format expected by the AI
    """
    conversation_messages = []

    # Add history messages
    for msg in request.history:
        conversation_messages.append({
            "role": msg.role,
            "content": msg.content
        })

    # Add current message
    conversation_messages.append({
        "role": "user",
        "content": request.message
    })

    return conversation_messages


async def execute_supabase_tool(function_name: str, function_args: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a Supabase tool function safely.

    Args:
        function_name: Name of the function to execute
        function_args: Arguments to pass to the function

    Returns:
        Result of the function execution

    Raises:
        HTTPException: If function name is not recognized or execution fails
    """
    try:
        from app.tools.supabase_tools import (
            get_asset_data_from_db,
            find_assets_by_criteria,
            get_market_summary,
            get_technical_analysis_summary
        )

        tool_map = {
            "get_asset_data_from_db": get_asset_data_from_db,
            "find_assets_by_criteria": find_assets_by_criteria,
            "get_market_summary": get_market_summary,
            "get_technical_analysis_summary": get_technical_analysis_summary
        }

        if function_name not in tool_map:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown tool: {function_name}"
            )

        tool_function = tool_map[function_name]

        # Execute the function with the provided arguments
        result = await tool_function(**function_args)

        logger.info(f"Successfully executed tool {function_name}")
        return result

    except Exception as e:
        logger.error(f"Error executing tool {function_name}: {e}")
        return {
            "error": f"Error executing {function_name}: {str(e)}",
            "function_name": function_name,
            "function_args": function_args
        }

async def _process_chat_request_with_function_calling(
    request: ChatRequest,
    current_user: Dict[str, Any]
) -> ChatResponse:
    """
    Process a chat request with dynamic function calling.

    Args:
        request: Chat request containing message and history
        current_user: User information

    Returns:
        ChatResponse: AI-generated response
    """
    MAX_TOOL_CALLS = 3
    tool_calls_count = 0

    try:
        logger.info(f"Chat request from user: {current_user.get('email', 'unknown')}")

        # Initialize Gemini model WITH dynamic tools
        from app.services.vertex_ai import initialize_gemini_model_with_dynamic_tools
        model = await initialize_gemini_model_with_dynamic_tools()

        if model is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to initialize AI model"
            )

        # 1. Preparar el historial de conversación usando los tipos de Vertex AI
        from vertexai.generative_models import Content, Part

        conversation_history: List[Content] = []
        for msg in request.history:
            role = "model" if msg.role == "assistant" else "user"
            conversation_history.append(Content(role=role, parts=[Part.from_text(msg.content)]))

        conversation_history.append(Content(role="user", parts=[Part.from_text(request.message)]))

        # 2. Bucle de conversación y llamada a funciones
        while tool_calls_count < MAX_TOOL_CALLS:
            logger.info(f"Generating response, iteration {tool_calls_count + 1}")

            response = await generate_chat_response(conversation_history, model)

            # Validar que la respuesta es válida
            if not response or not response.candidates or len(response.candidates) == 0:
                logger.error("Invalid response from AI model: no candidates")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Invalid response from AI model"
                )

            # Extraer la primera parte candidata de la respuesta
            candidate = response.candidates[0]

            # Validar que el candidato tiene contenido
            if not candidate or not candidate.content:
                logger.error("Invalid candidate: no content")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Invalid response structure from AI model"
                )

            # Verificar si la respuesta contiene una llamada a función
            if candidate.function_calls:
                logger.info(f"Function call requested by model: {[fc.name for fc in candidate.function_calls]}")

                # PASO CRÍTICO 1: Añadir la respuesta completa del modelo al historial
                # Esto incluye la thought_signature y la function_call request
                conversation_history.append(candidate.content)
                logger.info(f"Added model's function call request to conversation history. Total parts: {len(candidate.content.parts)}")
                logger.debug(f"Conversation history length after adding model request: {len(conversation_history)}")

                # PASO CRÍTICO 2: Ejecutar todas las funciones solicitadas
                function_response_parts = []
                for function_call in candidate.function_calls:
                    function_name = function_call.name

                    # Manejar function_call.args de forma segura
                    if hasattr(function_call.args, 'items'):
                        # Es un diccionario
                        function_args = {key: value for key, value in function_call.args.items()}
                    elif isinstance(function_call.args, dict):
                        # Es un diccionario pero sin método items visible
                        function_args = dict(function_call.args)
                    else:
                        # Convertir a diccionario si es otro tipo
                        function_args = dict(function_call.args) if function_call.args else {}

                    logger.info(f"Executing tool: {function_name} with args: {function_args}")

                    try:
                        logger.debug(f"Executing tool {function_name} with args: {function_args}")
                        tool_result = await execute_supabase_tool(function_name, function_args)
                        function_response_parts.append(Part.from_function_response(
                            name=function_name,
                            response=tool_result  # Enviar resultado directo, no wrapped en "content"
                        ))
                        logger.info(f"Successfully executed tool {function_name}")
                        logger.debug(f"Tool {function_name} result type: {type(tool_result)}")
                    except Exception as e:
                        logger.error(f"Error executing tool {function_name}: {e}")
                        logger.debug(f"Tool execution error details: {str(e)}")
                        function_response_parts.append(Part.from_function_response(
                            name=function_name,
                            response={"error": f"Error executing tool: {str(e)}"}
                        ))

                # PASO CRÍTICO 3: Añadir los resultados de las funciones al historial
                conversation_history.append(Content(parts=function_response_parts))
                logger.info(f"Added {len(function_response_parts)} function results to conversation history")
                logger.debug(f"Conversation history length after adding function results: {len(conversation_history)}")
                logger.debug(f"Function response parts created: {[part.function_response.name for part in function_response_parts if hasattr(part, 'function_response')]}")

                tool_calls_count += 1
                logger.info(f"Tool calls count incremented to: {tool_calls_count}/{MAX_TOOL_CALLS}")

            # Verificar si el modelo responde con texto (manejo seguro)
            else:
                # Intentar obtener texto de forma segura
                try:
                    if candidate.text:
                        final_reply = candidate.text
                        logger.info(f"Final text response received from model. Length: {len(final_reply)} characters")
                        logger.debug(f"Final response preview: {final_reply[:100]}...")
                        break
                except (AttributeError, ValueError):
                    # No hay texto disponible, verificar thought_signature
                    pass

                # Verificar si hay thought_signature (modelo pensando)
                has_thought_signature = any(
                    hasattr(part, 'thought_signature') or 'thought_signature' in str(part)
                    for part in candidate.content.parts
                )

                if has_thought_signature:
                    logger.info("Model returned thought_signature only, adding to conversation and continuing...")
                    # Añadir la respuesta del modelo al historial para mantener contexto
                    conversation_history.append(candidate.content)

                    # Añadir una instrucción específica para que use las herramientas
                    from vertexai.generative_models import Content, Part
                    prompt_instruction = Content(
                        role="user",
                        parts=[Part.from_text("Ahora usa las herramientas disponibles para obtener los datos solicitados.")]
                    )
                    conversation_history.append(prompt_instruction)
                    logger.info("Added instruction to use tools after thought_signature")

                    # Continuar el bucle para que el modelo pueda responder
                    continue
                else:
                    logger.warning("Model response has no text, function calls, or thought signatures. Exiting loop.")
                    final_reply = "Lo siento, no pude procesar tu solicitud correctamente. Esta información es solo para fines educativos."
                    break

        # Manejar casos especiales de finalización
        if tool_calls_count >= MAX_TOOL_CALLS:
            logger.warning(f"Max tool calls ({MAX_TOOL_CALLS}) exceeded. Forcing final response.")
            # Cuando se excede el máximo, forzar una respuesta final
            final_reply = "He realizado varias consultas para procesar tu solicitud. Basándome en la información disponible, puedo ayudarte con análisis financiero. ¿Podrías ser más específico? Esta información es solo para fines educativos."

        # Si no hay respuesta final después del bucle, generar una
        if not final_reply:
            logger.warning("No final reply generated. Creating fallback response.")
            final_reply = "Lo siento, no pude procesar tu solicitud correctamente. Esta información es solo para fines educativos."

        # Create response object
        conversation_id = f"conv_{current_user['id'][:8]}"
        response = ChatResponse(
            reply=final_reply,
            timestamp=datetime.now(),
            conversation_id=conversation_id
        )

        # Guardar en historial
        try:
            # Convertir conversation_history de vuelta a formato simple para guardar
            simple_history = []
            for content in conversation_history:
                if content.role == "user":
                    simple_history.append({"role": "user", "content": content.parts[0].text})
                elif content.role == "model":
                    if content.parts and hasattr(content.parts[0], 'text') and content.parts[0].text:
                        simple_history.append({"role": "assistant", "content": content.parts[0].text})

            await save_chat_history(
                user_id=current_user["id"],
                request_messages=simple_history,
                ai_response=final_reply,
                conversation_id=conversation_id
            )
        except Exception as save_error:
            logger.error(f"Failed to save chat history: {save_error}")

        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@router.get("/health")
async def chat_health():
    """Health check endpoint for the chat service."""
    return {
        "status": "healthy",
        "service": "chat",
        "timestamp": datetime.now().isoformat()
    }


@router.get("/market-analysis")
async def get_market_analysis(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Genera análisis general de mercado con tendencias y alertas.
    """
    try:
        logger.info(f"Market analysis request from user: {current_user.get('email', 'unknown')}")

        analysis = await market_data_service.generate_market_analysis()

        return {
            "analysis": analysis,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating market analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate market analysis"
        )


@router.get("/buy-opportunities")
async def get_buy_opportunities(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Analiza todos los activos y genera recomendaciones de compra
    basadas en múltiples indicadores técnicos.
    """
    try:
        logger.info(f"Buy opportunities request from user: {current_user.get('email', 'unknown')}")

        opportunities = await market_data_service.generate_buy_opportunities()

        return {
            "opportunities": opportunities,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating buy opportunities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate buy opportunities"
        )


# Debug endpoints (sin autenticación para testing)
@router.api_route("/market-analysis/debug", methods=["GET", "OPTIONS"])
async def get_market_analysis_debug():
    """
    Genera análisis general de mercado (DEBUG - sin autenticación).
    """
    try:
        analysis = await market_data_service.generate_market_analysis()

        return {
            "analysis": analysis,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating market analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate market analysis"
        )


@router.api_route("/buy-opportunities/debug", methods=["GET", "OPTIONS"])
async def get_buy_opportunities_debug():
    """
    Genera oportunidades de compra (DEBUG - sin autenticación).
    """
    try:
        opportunities = await market_data_service.generate_buy_opportunities()

        return {
            "opportunities": opportunities,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating buy opportunities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate buy opportunities"
        )


@router.post("/update-market-data/debug")
async def update_market_data_debug(
    batch_size: int = 10,
    specific_symbols: str = None
):
    """
    Actualiza manualmente los datos de mercado (DEBUG - sin autenticación).

    Args:
        batch_size: Tamaño del lote para procesamiento (default: 10)
        specific_symbols: Símbolos específicos separados por coma (opcional)
    """
    try:
        logger.info("🔄 Iniciando actualización manual de datos de mercado...")

        # Procesar símbolos específicos si se proporcionan
        if specific_symbols:
            symbols_list = [s.strip() for s in specific_symbols.split(",")]
            logger.info(f"📊 Actualizando símbolos específicos: {symbols_list}")
            result = await market_data_service.update_specific_assets(symbols_list, batch_size=batch_size)
        else:
            logger.info(f"📊 Actualizando todos los activos con batch_size: {batch_size}")
            result = await market_data_service.update_market_data(batch_size=batch_size)

        return {
            "message": "Actualización completada",
            "result": result,
            "parameters": {
                "batch_size": batch_size,
                "specific_symbols": specific_symbols,
                "symbols_processed": len(symbols_list) if specific_symbols else "all"
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error updating market data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update market data"
        )


@router.post("/scheduler/start")
async def start_scheduler(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Inicia el scheduler automático de datos de mercado.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        logger.info(f"Start scheduler request from user: {current_user.get('email', 'unknown')}")

        result = scheduler_service.start()

        return {
            "message": "Scheduler start command executed",
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error starting scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start scheduler"
        )


@router.post("/scheduler/stop")
async def stop_scheduler(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Detiene el scheduler automático de datos de mercado.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        logger.info(f"Stop scheduler request from user: {current_user.get('email', 'unknown')}")

        result = scheduler_service.stop()

        return {
            "message": "Scheduler stop command executed",
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error stopping scheduler: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop scheduler"
        )


@router.get("/scheduler/status")
async def get_scheduler_status(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Obtiene el estado actual del scheduler.
    """
    try:
        from app.services.scheduler_service import scheduler_service

        status_info = scheduler_service.get_status()

        return {
            "scheduler_status": status_info,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting scheduler status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get scheduler status"
        )


@router.post("/advanced-analysis/update")
async def update_advanced_analysis(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Ejecuta análisis avanzados (volumen, patrones, multi-timeframe, etc.) y los almacena en Supabase.
    """
    try:
        from app.services.market_data_service import market_data_service

        # Ejecutar análisis avanzados
        results = await market_data_service.update_advanced_analysis(batch_size=3)

        return {
            "message": "Análisis avanzados actualizados exitosamente",
            "results": results,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error updating advanced analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update advanced analysis: {str(e)}"
        )


@router.get("/advanced-analysis/status")
async def get_advanced_analysis_status(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Obtiene el estado de los análisis avanzados almacenados.
    """
    try:
        from app.services.supabase_client import get_supabase_client

        supabase = get_supabase_client()
        cutoff_time = (datetime.now() - timedelta(hours=24)).isoformat()

        # Contar registros en cada tabla
        volume_count = supabase.table("volume_analysis").select("id", count="exact").gte(
            "analyzed_at", cutoff_time
        ).execute()

        patterns_count = supabase.table("pattern_analysis").select("id", count="exact").gte(
            "detected_at", cutoff_time
        ).execute()

        mtf_count = supabase.table("multi_timeframe_analysis").select("id", count="exact").gte(
            "analyzed_at", cutoff_time
        ).execute()

        sr_count = supabase.table("support_resistance_levels").select("id", count="exact").gte(
            "identified_at", cutoff_time
        ).execute()

        ms_count = supabase.table("market_structure_analysis").select("id", count="exact").gte(
            "analyzed_at", cutoff_time
        ).execute()

        return {
            "advanced_analysis_status": {
                "volume_analysis_24h": volume_count.count,
                "patterns_detected_24h": patterns_count.count,
                "multi_timeframe_analysis_24h": mtf_count.count,
                "support_resistance_levels_24h": sr_count.count,
                "market_structure_analysis_24h": ms_count.count,
                "last_updated": cutoff_time
            },
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting advanced analysis status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get advanced analysis status"
        )


@router.post("/full-data-reload")
async def full_data_reload(current_user: Dict[str, Any] = Depends(get_current_user)):
    """
    Ejecuta una recarga completa de todos los datos de mercado.
    Incluye datos básicos, indicadores técnicos y análisis avanzados.
    """
    try:
        from app.services.market_data_service import market_data_service

        logger.info(f"Full data reload request from user: {current_user.get('email', 'unknown')}")

        # Ejecutar recarga completa
        results = await market_data_service.full_data_reload()

        return {
            "message": "Recarga completa de datos ejecutada exitosamente",
            "results": results,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error in full data reload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute full data reload: {str(e)}"
        )
