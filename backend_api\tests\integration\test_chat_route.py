"""
Integration tests for chat routes.

This module contains integration tests for the chat endpoint,
testing the complete flow with mocked external services.
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from datetime import datetime

from app.main import app
from app.models.chat import ChatMessage


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock user data for authentication tests."""
    return {
        "id": "user_123456789",
        "email": "<EMAIL>",
        "email_confirmed_at": "2025-01-01T00:00:00Z",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "user_metadata": {},
        "app_metadata": {}
    }


@pytest.fixture
def valid_chat_request():
    """Valid chat request payload."""
    return {
        "history": [
            {
                "role": "user",
                "content": "Hola",
                "timestamp": "2025-01-01T10:00:00Z"
            },
            {
                "role": "assistant", 
                "content": "¡Hola! ¿En qué puedo ayudarte?",
                "timestamp": "2025-01-01T10:00:05Z"
            }
        ],
        "message": "¿Cuál es el precio de Tesla?"
    }


class TestChatEndpointAuthentication:
    """Test authentication for chat endpoint."""
    
    def test_chat_unauthenticated(self, client):
        """Test that chat endpoint returns 401 without authentication."""
        response = client.post("/api/v1/chat/", json={"message": "test", "history": []})
        
        assert response.status_code == 403  # FastAPI returns 403 for missing auth
    
    def test_chat_invalid_token(self, client):
        """Test that chat endpoint returns 401 with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.post(
            "/api/v1/chat/", 
            json={"message": "test", "history": []},
            headers=headers
        )
        
        assert response.status_code == 401
    
    @patch('app.services.supabase_client.validate_user_token')
    def test_chat_valid_token_but_validation_fails(self, mock_validate, client):
        """Test chat endpoint when token validation fails."""
        from fastapi import HTTPException, status
        
        mock_validate.side_effect = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
        
        headers = {"Authorization": "Bearer valid_but_expired_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "test", "history": []},
            headers=headers
        )
        
        assert response.status_code == 401


class TestChatEndpointFlow:
    """Test the complete chat flow with mocked services."""
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.get_price_data')
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_with_tool_call_success(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_get_price_data,
        mock_save_history,
        client,
        mock_user,
        valid_chat_request
    ):
        """Test successful chat flow with tool call."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock AI requesting a tool call
        mock_generate_response.return_value = {
            "type": "function_call",
            "function_name": "get_price_data",
            "function_args": {
                "symbol": "NASDAQ:TSLA",
                "interval": "1D",
                "n_bars": 1
            }
        }
        
        # Mock tool execution result
        mock_get_price_data.return_value = {
            "symbol": "NASDAQ:TSLA",
            "interval": "1D",
            "bars_count": 1,
            "latest_price": 248.50,
            "data": [{
                "datetime": "2025-01-01T00:00:00",
                "open": 245.0,
                "high": 250.0,
                "low": 244.0,
                "close": 248.50,
                "volume": 1000000
            }]
        }
        

        
        # Mock save history (async)
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json=valid_chat_request,
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "timestamp" in response_data
        assert "conversation_id" in response_data
        assert "Tesla" in response_data["reply"]
        assert "$248.50" in response_data["reply"]
        assert "asesoramiento financiero" in response_data["reply"]
        
        # Verify mocks were called
        mock_validate.assert_called_once()
        mock_init_model.assert_called_once()
        mock_generate_response.assert_called_once()
        mock_get_price_data.assert_called_once_with(
            symbol="NASDAQ:TSLA",
            interval="1D", 
            n_bars=1
        )
        mock_send_result.assert_called_once()
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_direct_text_response(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_save_history,
        client,
        mock_user,
        valid_chat_request
    ):
        """Test chat flow with direct text response (no tool calls)."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock direct text response
        mock_generate_response.return_value = {
            "type": "text",
            "content": "¡Hola! Soy tu asistente financiero. ¿En qué puedo ayudarte hoy? Esta información es solo para fines educativos y no constituye asesoramiento financiero."
        }
        
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "Hola", "history": []},
            headers=headers
        )
        
        # Assertions
        assert response.status_code == 200
        
        response_data = response.json()
        assert "reply" in response_data
        assert "asistente financiero" in response_data["reply"]
        assert "asesoramiento financiero" in response_data["reply"]
    
    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.get_price_data')
    @patch('app.routes.chat.generate_chat_response', new_callable=AsyncMock)
    @patch('app.routes.chat.initialize_gemini_model')
    @patch('app.routes.chat.validate_user_token', new_callable=AsyncMock)
    def test_chat_tool_call_error_handling(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_get_price_data,
        mock_save_history,
        client,
        mock_user,
        valid_chat_request
    ):
        """Test error handling when tool execution fails."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model
        
        # Mock AI requesting a tool call
        mock_generate_response.return_value = {
            "type": "function_call",
            "function_name": "get_price_data",
            "function_args": {
                "symbol": "INVALID:SYMBOL",
                "interval": "1D",
                "n_bars": 1
            }
        }
        
        # Mock tool execution failure
        from fastapi import HTTPException
        mock_get_price_data.side_effect = HTTPException(
            status_code=404,
            detail="No data found for symbol"
        )
        
        mock_save_history.return_value = AsyncMock()
        
        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json=valid_chat_request,
            headers=headers
        )
        
        # Should still return 200 but with error message
        assert response.status_code == 200
        
        response_data = response.json()
        assert "error" in response_data["reply"].lower()
        assert "asesoramiento financiero" in response_data["reply"]


class TestChatHealthEndpoint:
    """Test the chat health endpoint."""
    
    def test_chat_health(self, client):
        """Test that chat health endpoint works."""
        response = client.get("/api/v1/chat/health")

        assert response.status_code == 200

        response_data = response.json()
        assert response_data["status"] == "healthy"
        assert response_data["service"] == "chat"
        assert "timestamp" in response_data


class TestChatEndpointWithFunctionCalling:
    """Test the new chat flow with function calling."""

    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_with_single_tool_call(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow: question → tool call → final response."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model

        # First call: AI requests tool
        # Second call: AI provides final response
        mock_generate_response.side_effect = [
            {
                "type": "function_call",
                "function_name": "get_asset_data_from_db",
                "function_args": {"symbol": "NASDAQ:TSLA"}
            },
            {
                "type": "text",
                "content": "Basándome en los datos actuales de Tesla (NASDAQ:TSLA), el precio actual es de $250.00 con un RSI de 65, indicando una condición neutral. Esta información es solo para fines educativos."
            }
        ]

        # Mock tool execution
        mock_execute_tool.return_value = {
            "symbol": "NASDAQ:TSLA",
            "market_data": {"current_price": 250.0},
            "technical_indicators": {"RSI": {"value": 65.0, "interpretation": "neutral"}}
        }

        mock_save_history.return_value = None

        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "¿Cuál es el precio actual de Tesla?", "history": []},
            headers=headers
        )

        # Assertions
        assert response.status_code == 200

        response_data = response.json()
        assert "reply" in response_data
        assert "Tesla" in response_data["reply"]
        assert "250" in response_data["reply"]
        assert "educativos" in response_data["reply"]

        # Verify function calls
        assert mock_generate_response.call_count == 2
        mock_execute_tool.assert_called_once_with("get_asset_data_from_db", {"symbol": "NASDAQ:TSLA"})
        mock_save_history.assert_called_once()

    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_with_multiple_tool_calls(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow with multiple tool calls."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model

        # Three calls: two tool calls + final response
        mock_generate_response.side_effect = [
            {
                "type": "function_call",
                "function_name": "get_market_summary",
                "function_args": {"category": "tech"}
            },
            {
                "type": "function_call",
                "function_name": "find_assets_by_criteria",
                "function_args": {"criteria": {"indicator": "RSI", "condition": "oversold", "value": 30}}
            },
            {
                "type": "text",
                "content": "Basándome en el análisis del mercado tecnológico y los activos en sobreventa, encontré 3 oportunidades interesantes. Esta información es solo para fines educativos."
            }
        ]

        # Mock tool executions
        mock_execute_tool.side_effect = [
            {"category": "tech", "total_assets": 50, "gainers_count": 30, "losers_count": 20},
            ["NASDAQ:AAPL", "NASDAQ:MSFT", "NASDAQ:GOOGL"]
        ]

        mock_save_history.return_value = None

        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "¿Qué oportunidades de compra hay en el sector tecnológico?", "history": []},
            headers=headers
        )

        # Assertions
        assert response.status_code == 200

        response_data = response.json()
        assert "reply" in response_data
        assert "oportunidades" in response_data["reply"]
        assert "educativos" in response_data["reply"]

        # Verify function calls
        assert mock_generate_response.call_count == 3
        assert mock_execute_tool.call_count == 2
        mock_save_history.assert_called_once()

    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_with_tool_error(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow when tool execution fails."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model

        # AI requests tool, then provides response based on error
        mock_generate_response.side_effect = [
            {
                "type": "function_call",
                "function_name": "get_asset_data_from_db",
                "function_args": {"symbol": "INVALID:SYMBOL"}
            },
            {
                "type": "text",
                "content": "Lo siento, no pude obtener los datos para ese símbolo. Por favor verifica que el símbolo sea correcto. Esta información es solo para fines educativos."
            }
        ]

        # Mock tool execution error
        mock_execute_tool.return_value = {
            "error": "Error executing get_asset_data_from_db: Symbol not found"
        }

        mock_save_history.return_value = None

        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "¿Cuál es el precio de INVALID:SYMBOL?", "history": []},
            headers=headers
        )

        # Assertions
        assert response.status_code == 200

        response_data = response.json()
        assert "reply" in response_data
        assert "Lo siento" in response_data["reply"]
        assert "educativos" in response_data["reply"]

        # Verify function calls
        assert mock_generate_response.call_count == 2
        mock_execute_tool.assert_called_once()
        mock_save_history.assert_called_once()

    @patch('app.routes.chat.save_chat_history', new_callable=AsyncMock)
    @patch('app.routes.chat.execute_supabase_tool', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.generate_chat_response', new_callable=AsyncMock)
    @patch('app.services.vertex_ai.initialize_gemini_model_with_dynamic_tools', new_callable=AsyncMock)
    @patch('app.services.supabase_client.validate_user_token', new_callable=AsyncMock)
    @pytest.mark.asyncio
    async def test_chat_max_tool_calls_exceeded(
        self,
        mock_validate,
        mock_init_model,
        mock_generate_response,
        mock_execute_tool,
        mock_save_history,
        client,
        mock_user
    ):
        """Test chat flow when maximum tool calls are exceeded."""
        # Setup mocks
        mock_validate.return_value = mock_user
        mock_model = MagicMock()
        mock_init_model.return_value = mock_model

        # AI keeps requesting tools (simulate infinite loop)
        mock_generate_response.return_value = {
            "type": "function_call",
            "function_name": "get_asset_data_from_db",
            "function_args": {"symbol": "NASDAQ:TSLA"}
        }

        # Mock tool execution
        mock_execute_tool.return_value = {"symbol": "NASDAQ:TSLA", "data": "some_data"}

        mock_save_history.return_value = None

        # Make request
        headers = {"Authorization": "Bearer valid_token"}
        response = client.post(
            "/api/v1/chat/",
            json={"message": "Analiza Tesla", "history": []},
            headers=headers
        )

        # Assertions
        assert response.status_code == 200

        response_data = response.json()
        assert "reply" in response_data
        assert "múltiples fuentes" in response_data["reply"]
        assert "educativos" in response_data["reply"]

        # Verify maximum tool calls were made
        assert mock_generate_response.call_count == 3  # MAX_TOOL_CALLS
        assert mock_execute_tool.call_count == 3
        mock_save_history.assert_called_once()
