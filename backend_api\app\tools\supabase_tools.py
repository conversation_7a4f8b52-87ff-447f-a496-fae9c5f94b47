"""
Supabase Tools - Herramientas para acceso dinámico a datos de mercado
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.services.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


async def get_asset_data_from_db(symbol: str) -> Dict[str, Any]:
    """
    Obtiene datos completos de un activo desde todas las tablas de la base de datos.
    
    Args:
        symbol: Símbolo del activo (ej: 'NASDAQ:TSLA')
        
    Returns:
        Dict con todos los datos disponibles del activo
    """
    try:
        supabase = get_supabase_client()
        result = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "data_sources": []
        }
        
        # 1. Datos básicos de mercado
        market_data = supabase.table("market_data").select("*").eq("symbol", symbol).execute()
        if market_data.data:
            result["market_data"] = market_data.data[0]
            result["data_sources"].append("market_data")
            
        # 2. Indicadores técnicos
        indicators = supabase.table("technical_indicators").select("*").eq("symbol", symbol).execute()
        if indicators.data:
            result["technical_indicators"] = {}
            for indicator in indicators.data:
                result["technical_indicators"][indicator["indicator_name"]] = {
                    "value": indicator["value"],
                    "interpretation": indicator["interpretation"],
                    "calculated_at": indicator["calculated_at"]
                }
            result["data_sources"].append("technical_indicators")
            
        # 3. Análisis de volumen
        volume_analysis = supabase.table("volume_analysis").select("*").eq("symbol", symbol).execute()
        if volume_analysis.data:
            result["volume_analysis"] = volume_analysis.data[0]
            result["data_sources"].append("volume_analysis")
            
        # 4. Análisis de patrones
        patterns = supabase.table("pattern_analysis").select("*").eq("symbol", symbol).execute()
        if patterns.data:
            result["pattern_analysis"] = patterns.data
            result["data_sources"].append("pattern_analysis")
            
        # 5. Análisis multi-timeframe
        mtf_analysis = supabase.table("multi_timeframe_analysis").select("*").eq("symbol", symbol).execute()
        if mtf_analysis.data:
            result["multi_timeframe_analysis"] = mtf_analysis.data[0]
            result["data_sources"].append("multi_timeframe_analysis")
            
        # 6. Niveles de soporte y resistencia
        sr_levels = supabase.table("support_resistance_levels").select("*").eq("symbol", symbol).execute()
        if sr_levels.data:
            result["support_resistance_levels"] = {
                "support": [level for level in sr_levels.data if level["level_type"] == "SUPPORT"],
                "resistance": [level for level in sr_levels.data if level["level_type"] == "RESISTANCE"]
            }
            result["data_sources"].append("support_resistance_levels")
            
        # 7. Estructura de mercado
        market_structure = supabase.table("market_structure_analysis").select("*").eq("symbol", symbol).execute()
        if market_structure.data:
            result["market_structure_analysis"] = market_structure.data[0]
            result["data_sources"].append("market_structure_analysis")
            
        logger.info(f"Retrieved data for {symbol} from {len(result['data_sources'])} sources")
        return result
        
    except Exception as e:
        logger.error(f"Error retrieving asset data for {symbol}: {e}")
        return {
            "symbol": symbol,
            "error": f"Error retrieving data: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def find_assets_by_criteria(criteria: Dict[str, Any]) -> List[str]:
    """
    Busca activos que cumplan criterios específicos.
    
    Args:
        criteria: Diccionario con criterios de búsqueda
        Ejemplos:
        - {"indicator": "RSI", "condition": "oversold", "value": 30}
        - {"pattern": "bullish_flag", "confidence": 80}
        - {"volume_confirmation": True}
        
    Returns:
        Lista de símbolos que cumplen los criterios
    """
    try:
        supabase = get_supabase_client()
        matching_symbols = set()
        
        # Búsqueda por indicadores técnicos
        if "indicator" in criteria:
            indicator_name = criteria["indicator"]
            condition = criteria.get("condition", "")
            value = criteria.get("value", 0)
            
            query = supabase.table("technical_indicators").select("symbol").eq("indicator_name", indicator_name)
            
            if condition == "oversold" and indicator_name == "RSI":
                query = query.lt("value", value or 30)
            elif condition == "overbought" and indicator_name == "RSI":
                query = query.gt("value", value or 70)
            elif condition == "above":
                query = query.gt("value", value)
            elif condition == "below":
                query = query.lt("value", value)
                
            result = query.execute()
            matching_symbols.update([item["symbol"] for item in result.data])
            
        # Búsqueda por patrones
        if "pattern" in criteria:
            pattern_type = criteria["pattern"]
            min_confidence = criteria.get("confidence", 0)
            
            query = supabase.table("pattern_analysis").select("symbol").eq("pattern_type", pattern_type)
            if min_confidence > 0:
                query = query.gte("confidence_score", min_confidence)
                
            result = query.execute()
            if matching_symbols:
                matching_symbols.intersection_update([item["symbol"] for item in result.data])
            else:
                matching_symbols.update([item["symbol"] for item in result.data])
                
        # Búsqueda por confirmación de volumen
        if "volume_confirmation" in criteria:
            confirmation = criteria["volume_confirmation"]
            
            query = supabase.table("volume_analysis").select("symbol").eq("volume_confirmation", confirmation)
            result = query.execute()
            
            if matching_symbols:
                matching_symbols.intersection_update([item["symbol"] for item in result.data])
            else:
                matching_symbols.update([item["symbol"] for item in result.data])
                
        # Búsqueda por tendencia de mercado
        if "trend" in criteria:
            trend = criteria["trend"]
            
            query = supabase.table("market_structure_analysis").select("symbol").eq("trend_structure", trend)
            result = query.execute()
            
            if matching_symbols:
                matching_symbols.intersection_update([item["symbol"] for item in result.data])
            else:
                matching_symbols.update([item["symbol"] for item in result.data])
        
        symbols_list = list(matching_symbols)
        logger.info(f"Found {len(symbols_list)} assets matching criteria: {criteria}")
        return symbols_list
        
    except Exception as e:
        logger.error(f"Error finding assets by criteria {criteria}: {e}")
        return []


async def get_market_summary(category: str = "all") -> Dict[str, Any]:
    """
    Obtiene resumen del mercado por categorías.
    
    Args:
        category: Categoría del mercado ("tech", "crypto", "indices", "forex", "all")
        
    Returns:
        Dict con resumen del mercado para la categoría especificada
    """
    try:
        supabase = get_supabase_client()
        
        # Mapeo de categorías a prefijos de símbolos
        category_filters = {
            "tech": ["NASDAQ:", "NYSE:"],
            "crypto": ["BINANCE:", "COINBASE:"],
            "indices": ["INDEX:", "SPX:", "DJI:", "IXIC:"],
            "forex": ["FX:", "OANDA:"],
            "all": []
        }
        
        query = supabase.table("market_data").select("*")
        
        if category != "all" and category in category_filters:
            # Filtrar por prefijos de la categoría
            prefixes = category_filters[category]
            if prefixes:
                conditions = " OR ".join([f"symbol.like.{prefix}%" for prefix in prefixes])
                # Nota: Supabase no soporta OR directo, usaremos filtro en Python
                result = query.execute()
                filtered_data = []
                for item in result.data:
                    if any(item["symbol"].startswith(prefix) for prefix in prefixes):
                        filtered_data.append(item)
            else:
                filtered_data = query.execute().data
        else:
            filtered_data = query.execute().data
            
        # Calcular estadísticas del resumen
        if not filtered_data:
            return {
                "category": category,
                "total_assets": 0,
                "summary": "No data available",
                "timestamp": datetime.now().isoformat()
            }
            
        total_assets = len(filtered_data)
        gainers = [item for item in filtered_data if item.get("change_percent_24h", 0) > 0]
        losers = [item for item in filtered_data if item.get("change_percent_24h", 0) < 0]
        
        # Top gainers y losers
        top_gainers = sorted(gainers, key=lambda x: x.get("change_percent_24h", 0), reverse=True)[:5]
        top_losers = sorted(losers, key=lambda x: x.get("change_percent_24h", 0))[:5]
        
        summary = {
            "category": category,
            "total_assets": total_assets,
            "gainers_count": len(gainers),
            "losers_count": len(losers),
            "top_gainers": [
                {
                    "symbol": item["symbol"],
                    "name": item.get("name", ""),
                    "current_price": item.get("current_price"),
                    "change_percent_24h": item.get("change_percent_24h")
                }
                for item in top_gainers
            ],
            "top_losers": [
                {
                    "symbol": item["symbol"],
                    "name": item.get("name", ""),
                    "current_price": item.get("current_price"),
                    "change_percent_24h": item.get("change_percent_24h")
                }
                for item in top_losers
            ],
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Generated market summary for category: {category}")
        return summary
        
    except Exception as e:
        logger.error(f"Error generating market summary for {category}: {e}")
        return {
            "category": category,
            "error": f"Error generating summary: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


async def get_technical_analysis_summary(symbol: str) -> Dict[str, Any]:
    """
    Obtiene análisis técnico completo para un activo específico.
    
    Args:
        symbol: Símbolo del activo
        
    Returns:
        Dict con análisis técnico completo combinando indicadores, patrones y niveles S/R
    """
    try:
        # Obtener datos completos del activo
        asset_data = await get_asset_data_from_db(symbol)
        
        if "error" in asset_data:
            return asset_data
            
        # Construir resumen de análisis técnico
        analysis = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "overall_sentiment": "NEUTRAL",
            "confidence_score": 0,
            "key_levels": {},
            "indicators_summary": {},
            "patterns_detected": [],
            "volume_analysis": {},
            "recommendations": []
        }
        
        # Analizar indicadores técnicos
        if "technical_indicators" in asset_data:
            indicators = asset_data["technical_indicators"]
            bullish_signals = 0
            bearish_signals = 0
            total_indicators = len(indicators)
            
            for indicator_name, data in indicators.items():
                interpretation = data.get("interpretation", "").lower()
                if "bullish" in interpretation or "buy" in interpretation:
                    bullish_signals += 1
                elif "bearish" in interpretation or "sell" in interpretation:
                    bearish_signals += 1
                    
            analysis["indicators_summary"] = {
                "total_indicators": total_indicators,
                "bullish_signals": bullish_signals,
                "bearish_signals": bearish_signals,
                "neutral_signals": total_indicators - bullish_signals - bearish_signals
            }
            
        # Analizar niveles de soporte y resistencia
        if "support_resistance_levels" in asset_data:
            sr_data = asset_data["support_resistance_levels"]
            analysis["key_levels"] = {
                "support_levels": len(sr_data.get("support", [])),
                "resistance_levels": len(sr_data.get("resistance", [])),
                "strongest_support": max(sr_data.get("support", []), key=lambda x: x.get("strength", 0), default=None),
                "strongest_resistance": max(sr_data.get("resistance", []), key=lambda x: x.get("strength", 0), default=None)
            }
            
        # Analizar patrones detectados
        if "pattern_analysis" in asset_data:
            patterns = asset_data["pattern_analysis"]
            analysis["patterns_detected"] = [
                {
                    "pattern": pattern["pattern_type"],
                    "confidence": pattern.get("confidence_score", 0),
                    "target_price": pattern.get("target_price"),
                    "detected_at": pattern.get("detected_at")
                }
                for pattern in patterns
            ]
            
        # Analizar volumen
        if "volume_analysis" in asset_data:
            volume_data = asset_data["volume_analysis"]
            analysis["volume_analysis"] = {
                "trend": volume_data.get("volume_trend"),
                "confirmation": volume_data.get("volume_confirmation"),
                "vwap": volume_data.get("vwap_value"),
                "mfi": volume_data.get("mfi_value")
            }
            
        # Determinar sentimiento general
        if "indicators_summary" in analysis:
            indicators_summary = analysis["indicators_summary"]
            if indicators_summary["bullish_signals"] > indicators_summary["bearish_signals"]:
                analysis["overall_sentiment"] = "BULLISH"
                analysis["confidence_score"] = min(90, (indicators_summary["bullish_signals"] / max(1, indicators_summary["total_indicators"])) * 100)
            elif indicators_summary["bearish_signals"] > indicators_summary["bullish_signals"]:
                analysis["overall_sentiment"] = "BEARISH"
                analysis["confidence_score"] = min(90, (indicators_summary["bearish_signals"] / max(1, indicators_summary["total_indicators"])) * 100)
            else:
                analysis["overall_sentiment"] = "NEUTRAL"
                analysis["confidence_score"] = 50
                
        logger.info(f"Generated technical analysis summary for {symbol}")
        return analysis
        
    except Exception as e:
        logger.error(f"Error generating technical analysis for {symbol}: {e}")
        return {
            "symbol": symbol,
            "error": f"Error generating analysis: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


# Tool definitions for Vertex AI
SUPABASE_TOOLS = [
    {
        "name": "get_asset_data_from_db",
        "description": "Obtiene datos completos de un activo desde la base de datos incluyendo precios, indicadores técnicos, análisis de volumen, patrones, niveles de soporte/resistencia y estructura de mercado",
        "parameters": {
            "type": "object",
            "properties": {
                "symbol": {
                    "type": "string",
                    "description": "Símbolo del activo (ej: 'NASDAQ:TSLA', 'BINANCE:BTCUSDT')"
                }
            },
            "required": ["symbol"]
        }
    },
    {
        "name": "find_assets_by_criteria",
        "description": "Busca activos que cumplan criterios específicos como indicadores técnicos, patrones, confirmación de volumen o tendencias",
        "parameters": {
            "type": "object",
            "properties": {
                "criteria": {
                    "type": "object",
                    "description": "Criterios de búsqueda",
                    "properties": {
                        "indicator": {
                            "type": "string",
                            "description": "Nombre del indicador técnico (RSI, MACD, SMA, etc.)"
                        },
                        "condition": {
                            "type": "string",
                            "description": "Condición del indicador",
                            "enum": ["oversold", "overbought", "above", "below"]
                        },
                        "value": {
                            "type": "number",
                            "description": "Valor umbral para la condición"
                        },
                        "pattern": {
                            "type": "string",
                            "description": "Tipo de patrón a buscar"
                        },
                        "confidence": {
                            "type": "number",
                            "description": "Confianza mínima del patrón (0-100)"
                        },
                        "volume_confirmation": {
                            "type": "boolean",
                            "description": "Si requiere confirmación de volumen"
                        },
                        "trend": {
                            "type": "string",
                            "description": "Tendencia del mercado (bullish, bearish, sideways)"
                        }
                    }
                }
            },
            "required": ["criteria"]
        }
    },
    {
        "name": "get_market_summary",
        "description": "Obtiene resumen del mercado por categorías mostrando ganadores, perdedores y estadísticas generales",
        "parameters": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "description": "Categoría del mercado",
                    "enum": ["tech", "crypto", "indices", "forex", "all"],
                    "default": "all"
                }
            }
        }
    },
    {
        "name": "get_technical_analysis_summary",
        "description": "Obtiene análisis técnico completo para un activo específico combinando indicadores, patrones, niveles S/R y análisis de volumen",
        "parameters": {
            "type": "object",
            "properties": {
                "symbol": {
                    "type": "string",
                    "description": "Símbolo del activo para análisis técnico completo"
                }
            },
            "required": ["symbol"]
        }
    }
]


def get_supabase_tools():
    """Return the list of available Supabase tools for Vertex AI."""
    return SUPABASE_TOOLS
