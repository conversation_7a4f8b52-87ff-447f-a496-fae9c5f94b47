"""
Unit tests for trading tools.

This module contains unit tests for the financial data and
technical analysis tools, using mocks to avoid external dependencies.
"""

import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
from datetime import datetime
from fastapi import HTTPException

from app.tools.tradingview_provider import get_price_data, apply_indicator


class TestGetPriceData:
    """Test cases for get_price_data function."""
    
    @patch('app.tools.tradingview_provider.tv')
    def test_get_price_data_success(self, mock_tv):
        """Test successful price data retrieval."""
        # Mock data
        mock_data = pd.DataFrame({
            'open': [100.0, 101.0, 102.0],
            'high': [105.0, 106.0, 107.0],
            'low': [99.0, 100.0, 101.0],
            'close': [104.0, 105.0, 106.0],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2025-01-01', periods=3, freq='D'))
        
        mock_tv.get_hist.return_value = mock_data
        
        # Test the function
        result = get_price_data("NASDAQ:TSLA", "1D", 3)
        
        # Assertions
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["interval"] == "1D"
        assert result["bars_count"] == 3
        assert len(result["data"]) == 3
        assert result["latest_price"] == 106.0
        
        # Check first bar data
        first_bar = result["data"][0]
        assert first_bar["open"] == 100.0
        assert first_bar["high"] == 105.0
        assert first_bar["low"] == 99.0
        assert first_bar["close"] == 104.0
        assert first_bar["volume"] == 1000
        
        # Verify mock was called correctly
        mock_tv.get_hist.assert_called_once_with(
            symbol="TSLA",
            exchange="NASDAQ",
            interval=mock_tv.get_hist.call_args[1]['interval'],
            n_bars=3
        )
    
    @patch('app.tools.tradingview_provider.tv')
    def test_get_price_data_no_exchange_prefix(self, mock_tv):
        """Test price data retrieval without exchange prefix."""
        mock_data = pd.DataFrame({
            'open': [100.0], 'high': [105.0], 'low': [99.0], 
            'close': [104.0], 'volume': [1000]
        }, index=pd.date_range('2025-01-01', periods=1, freq='D'))
        
        mock_tv.get_hist.return_value = mock_data
        
        result = get_price_data("TSLA", "1D", 1)
        
        assert result["symbol"] == "NASDAQ:TSLA"  # Should default to NASDAQ
        mock_tv.get_hist.assert_called_once_with(
            symbol="TSLA",
            exchange="NASDAQ",
            interval=mock_tv.get_hist.call_args[1]['interval'],
            n_bars=1
        )
    
    @patch('app.tools.tradingview_provider.tv')
    def test_get_price_data_no_data_found(self, mock_tv):
        """Test handling when no data is found."""
        mock_tv.get_hist.return_value = None
        
        with pytest.raises(HTTPException) as exc_info:
            get_price_data("INVALID:SYMBOL", "1D", 10)
        
        assert exc_info.value.status_code == 404
        assert "No data found" in str(exc_info.value.detail)
    
    def test_get_price_data_invalid_interval(self):
        """Test handling of invalid interval."""
        with pytest.raises(HTTPException) as exc_info:
            get_price_data("NASDAQ:TSLA", "INVALID", 10)
        
        assert exc_info.value.status_code == 400
        assert "Invalid interval" in str(exc_info.value.detail)
    
    def test_get_price_data_invalid_n_bars(self):
        """Test handling of invalid n_bars values."""
        # Test negative value
        with pytest.raises(HTTPException) as exc_info:
            get_price_data("NASDAQ:TSLA", "1D", -1)
        
        assert exc_info.value.status_code == 400
        assert "n_bars must be between 1 and 5000" in str(exc_info.value.detail)
        
        # Test too large value
        with pytest.raises(HTTPException) as exc_info:
            get_price_data("NASDAQ:TSLA", "1D", 6000)
        
        assert exc_info.value.status_code == 400


class TestApplyIndicator:
    """Test cases for apply_indicator function."""
    
    @patch('app.tools.tradingview_provider.get_price_data')
    @patch('pandas_ta.rsi')
    def test_apply_indicator_rsi_success(self, mock_rsi, mock_get_price_data):
        """Test successful RSI calculation."""
        # Mock price data
        mock_price_data = {
            "symbol": "NASDAQ:TSLA",
            "interval": "1D",
            "bars_count": 20,
            "data": [
                {
                    "datetime": "2025-01-01T00:00:00",
                    "open": 100.0, "high": 105.0, "low": 99.0, 
                    "close": 104.0, "volume": 1000
                }
            ] * 20  # 20 identical bars for simplicity
        }
        mock_get_price_data.return_value = mock_price_data
        
        # Mock RSI calculation
        mock_rsi_values = pd.Series([50.0, 55.0, 60.0] + [65.0] * 17)  # 20 values
        mock_rsi.return_value = mock_rsi_values
        
        # Test the function
        result = apply_indicator("NASDAQ:TSLA", "1D", "RSI", {"length": 14})
        
        # Assertions
        assert result["symbol"] == "NASDAQ:TSLA"
        assert result["interval"] == "1D"
        assert result["indicator"] == "RSI"
        assert result["parameters"]["length"] == 14
        assert len(result["values"]) == 20
        assert result["latest_value"]["value"] == 65.0
        
        # Verify mocks were called
        mock_get_price_data.assert_called_once_with("NASDAQ:TSLA", "1D", 100)
        mock_rsi.assert_called_once()
    
    @patch('app.tools.tradingview_provider.get_price_data')
    @patch('pandas_ta.macd')
    def test_apply_indicator_macd_success(self, mock_macd, mock_get_price_data):
        """Test successful MACD calculation."""
        # Mock price data
        mock_price_data = {
            "data": [{"datetime": "2025-01-01T00:00:00", "open": 100.0, 
                     "high": 105.0, "low": 99.0, "close": 104.0, "volume": 1000}] * 50
        }
        mock_get_price_data.return_value = mock_price_data
        
        # Mock MACD calculation (returns DataFrame)
        mock_macd_df = pd.DataFrame({
            'MACD_12_26_9': [1.0, 1.5, 2.0] + [2.5] * 47,
            'MACDh_12_26_9': [0.5, 0.7, 0.9] + [1.1] * 47,
            'MACDs_12_26_9': [0.8, 1.2, 1.6] + [2.0] * 47
        })
        mock_macd.return_value = mock_macd_df
        
        result = apply_indicator("NASDAQ:TSLA", "1D", "MACD", {"fast": 12, "slow": 26, "signal": 9})
        
        assert result["indicator"] == "MACD"
        assert len(result["values"]) == 50
        assert "macd_12_26_9" in result["latest_value"]
    
    def test_apply_indicator_unsupported(self):
        """Test handling of unsupported indicator."""
        with pytest.raises(HTTPException) as exc_info:
            apply_indicator("NASDAQ:TSLA", "1D", "UNSUPPORTED", {})
        
        assert exc_info.value.status_code == 400
        assert "Unsupported indicator" in str(exc_info.value.detail)
    
    @patch('app.tools.tradingview_provider.get_price_data')
    def test_apply_indicator_missing_pandas_ta(self, mock_get_price_data):
        """Test handling when pandas_ta is not available."""
        mock_get_price_data.return_value = {"data": []}
        
        with patch('builtins.__import__', side_effect=ImportError("No module named 'pandas_ta'")):
            with pytest.raises(HTTPException) as exc_info:
                apply_indicator("NASDAQ:TSLA", "1D", "RSI", {"length": 14})
            
            assert exc_info.value.status_code == 500
            assert "Technical analysis library not available" in str(exc_info.value.detail)



