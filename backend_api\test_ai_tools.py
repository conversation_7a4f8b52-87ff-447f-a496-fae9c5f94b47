#!/usr/bin/env python3
"""
Test script to verify AI has access to tools and uses them correctly.
"""

import asyncio
import json
from app.services.vertex_ai import initialize_gemini_model_with_dynamic_tools, generate_chat_response
from app.tools.supabase_tools import get_supabase_tools, get_asset_data_from_db
from app.routes.chat import execute_supabase_tool


async def test_tools_availability():
    """Test that tools are available and properly configured."""
    print("🔧 Testing Supabase tools availability...")
    
    tools = get_supabase_tools()
    print(f"✅ Found {len(tools)} tools:")
    for tool in tools:
        print(f"  - {tool['name']}: {tool['description'][:50]}...")
    
    return tools


async def test_model_initialization():
    """Test that Gemini model initializes with tools."""
    print("\n🤖 Testing Gemini model initialization...")
    
    model = await initialize_gemini_model_with_dynamic_tools()
    if model:
        print("✅ Gemini model initialized successfully with dynamic tools")
        print(f"✅ Model type: {type(model)}")
        return model
    else:
        print("❌ Failed to initialize Gemini model")
        return None


async def test_tool_execution():
    """Test that tools can be executed directly."""
    print("\n🔧 Testing direct tool execution...")
    
    try:
        # Test get_asset_data_from_db with a mock symbol
        print("Testing get_asset_data_from_db...")
        result = await execute_supabase_tool("get_asset_data_from_db", {"symbol": "NASDAQ:TSLA"})
        print(f"✅ Tool executed successfully")
        print(f"Result keys: {list(result.keys())}")
        
        if "error" in result:
            print(f"⚠️ Tool returned error (expected in test environment): {result['error']}")
        else:
            print(f"✅ Tool returned data for symbol: {result.get('symbol', 'unknown')}")
            
        return True
    except Exception as e:
        print(f"❌ Tool execution failed: {e}")
        return False


async def test_ai_tool_calling():
    """Test that AI can call tools through the conversation flow."""
    print("\n🤖 Testing AI tool calling...")
    
    model = await initialize_gemini_model_with_dynamic_tools()
    if not model:
        print("❌ Cannot test AI tool calling - model initialization failed")
        return False
    
    # Create a conversation that should trigger tool usage
    conversation_history = [
        {
            "role": "user",
            "content": "¿Cuál es el precio actual de Tesla (NASDAQ:TSLA)? Necesito datos específicos de la base de datos."
        }
    ]
    
    try:
        print("Sending request to AI...")
        response = await generate_chat_response(conversation_history, model)
        
        print(f"✅ AI Response received")
        print(f"Response type: {response.get('type', 'unknown')}")
        
        if response.get("type") == "function_call":
            print("✅ AI requested a function call!")
            print(f"Function: {response.get('function_name')}")
            print(f"Arguments: {response.get('function_args')}")
            
            # This proves the AI is trying to use tools
            return True
        elif response.get("type") == "text":
            print("⚠️ AI provided direct text response:")
            print(f"Content: {response.get('content', '')[:100]}...")
            
            # Check if the response mentions needing data or tools
            content = response.get('content', '').lower()
            if any(keyword in content for keyword in ['datos', 'información', 'consultar', 'base de datos']):
                print("✅ AI acknowledges need for data access")
                return True
            else:
                print("❌ AI did not attempt to use tools")
                return False
        else:
            print(f"❌ Unexpected response type: {response.get('type')}")
            return False
            
    except Exception as e:
        print(f"❌ AI tool calling test failed: {e}")
        return False


async def test_complete_flow():
    """Test the complete flow with tool execution."""
    print("\n🔄 Testing complete AI + Tool flow...")
    
    model = await initialize_gemini_model_with_dynamic_tools()
    if not model:
        print("❌ Cannot test complete flow - model initialization failed")
        return False
    
    conversation_history = [
        {
            "role": "user", 
            "content": "Busca activos con RSI en sobreventa (menor a 30)"
        }
    ]
    
    max_iterations = 3
    iteration = 0
    
    try:
        while iteration < max_iterations:
            iteration += 1
            print(f"\n--- Iteration {iteration} ---")
            
            response = await generate_chat_response(conversation_history, model)
            print(f"AI Response type: {response.get('type')}")
            
            if response.get("type") == "function_call":
                function_name = response.get("function_name")
                function_args = response.get("function_args")
                
                print(f"✅ AI called function: {function_name}")
                print(f"Arguments: {function_args}")
                
                # Execute the tool
                tool_result = await execute_supabase_tool(function_name, function_args)
                
                # Add result to conversation
                conversation_history.append({
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps(tool_result, ensure_ascii=False)
                })
                
                print(f"✅ Tool executed, result added to conversation")
                
            elif response.get("type") == "text":
                print("✅ AI provided final response:")
                print(f"Content: {response.get('content', '')[:200]}...")
                return True
            else:
                print(f"❌ Unexpected response type: {response.get('type')}")
                return False
        
        print("⚠️ Reached maximum iterations without final response")
        return False
        
    except Exception as e:
        print(f"❌ Complete flow test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting AI Tools Integration Test\n")
    
    # Test 1: Tools availability
    tools = await test_tools_availability()
    if not tools:
        print("❌ CRITICAL: No tools available")
        return
    
    # Test 2: Model initialization
    model = await test_model_initialization()
    if not model:
        print("❌ CRITICAL: Model initialization failed")
        return
    
    # Test 3: Direct tool execution
    tool_works = await test_tool_execution()
    if not tool_works:
        print("⚠️ WARNING: Direct tool execution failed")
    
    # Test 4: AI tool calling
    ai_calls_tools = await test_ai_tool_calling()
    if ai_calls_tools:
        print("✅ SUCCESS: AI can call tools")
    else:
        print("❌ CRITICAL: AI cannot call tools")
    
    # Test 5: Complete flow
    complete_flow_works = await test_complete_flow()
    if complete_flow_works:
        print("✅ SUCCESS: Complete AI + Tools flow works")
    else:
        print("❌ CRITICAL: Complete flow failed")
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    print(f"✅ Tools Available: {len(tools)} tools")
    print(f"✅ Model Initialized: {'Yes' if model else 'No'}")
    print(f"{'✅' if tool_works else '❌'} Direct Tool Execution: {'Working' if tool_works else 'Failed'}")
    print(f"{'✅' if ai_calls_tools else '❌'} AI Tool Calling: {'Working' if ai_calls_tools else 'Failed'}")
    print(f"{'✅' if complete_flow_works else '❌'} Complete Flow: {'Working' if complete_flow_works else 'Failed'}")
    
    if ai_calls_tools and complete_flow_works:
        print("\n🎉 ALL CRITICAL TESTS PASSED!")
        print("🤖 AI has access to tools and uses them correctly")
        print("🔧 No token reasoning issues detected")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("🔍 Review the output above for details")


if __name__ == "__main__":
    asyncio.run(main())
