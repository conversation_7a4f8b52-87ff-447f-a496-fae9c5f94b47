#!/usr/bin/env python3
"""
Test con logging detallado para ver exactamente qué pasa.
"""

import asyncio
import logging
from app.services.vertex_ai import generate_chat_response, initialize_gemini_model_with_dynamic_tools

# Configurar logging detallado
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')


async def test_with_detailed_logging():
    """Test con logging detallado."""
    print("🔍 TESTING WITH DETAILED LOGGING")
    print("="*50)
    
    # Inicializar modelo
    model = await initialize_gemini_model_with_dynamic_tools()
    
    if not model:
        print("❌ Error: No se pudo inicializar el modelo")
        return
    
    print(f"✅ Modelo inicializado: {model._model_name}")
    
    # Test simple
    messages = [
        {"role": "user", "content": "¿Cuál es el precio actual de Tesla (NASDAQ:TSLA)?"}
    ]
    
    print(f"\nProbando mensaje: {messages[0]['content']}")
    
    try:
        response = await generate_chat_response(messages, model)
        
        print(f"Respuesta recibida:")
        print(f"  Tipo: {response.get('type')}")
        print(f"  Contenido: {response.get('content', '')[:100]}...")
        
        if response.get('type') == 'function_call':
            print(f"  🎉 Function call: {response.get('function_name')}")
            print(f"  Argumentos: {response.get('function_args')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Ejecutar test."""
    await test_with_detailed_logging()


if __name__ == "__main__":
    asyncio.run(main())
