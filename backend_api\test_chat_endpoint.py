#!/usr/bin/env python3
"""
Test directo del endpoint de chat para verificar function calling.
"""

import asyncio
import json
from app.services.vertex_ai import generate_chat_response, initialize_gemini_model_with_dynamic_tools


async def test_chat_endpoint():
    """Test del flujo completo de chat con function calling."""
    print("🔍 TESTING CHAT ENDPOINT WITH FUNCTION CALLING")
    print("="*60)
    
    # 1. Inicializar modelo
    print("1. Inicializando modelo...")
    model = await initialize_gemini_model_with_dynamic_tools()
    
    if not model:
        print("❌ Error: No se pudo inicializar el modelo")
        return
    
    print(f"✅ Modelo inicializado: {model._model_name}")
    
    # 2. Probar diferentes mensajes usando Content y Part
    from vertexai.generative_models import Content, Part

    test_messages = [
        {
            "name": "Pregunta sobre Tesla",
            "contents": [
                Content(role="user", parts=[Part.from_text("¿Cuál es el precio actual de Tesla (NASDAQ:TSLA)?")])
            ]
        },
        {
            "name": "Búsqueda de activos",
            "contents": [
                Content(role="user", parts=[Part.from_text("Busca activos con RSI menor a 30")])
            ]
        },
        {
            "name": "Resumen de mercado",
            "contents": [
                Content(role="user", parts=[Part.from_text("Dame un resumen del mercado de tecnología")])
            ]
        }
    ]
    
    for i, test_case in enumerate(test_messages, 1):
        print(f"\n{i}. Probando: {test_case['name']}")
        print(f"   Mensaje: {test_case['contents'][0].parts[0].text}")

        try:
            # Llamar a generate_chat_response directamente
            response = await generate_chat_response(test_case['contents'], model)
            
            print(f"   ✅ Respuesta recibida")
            print(f"   Tipo: {type(response)}")

            # Verificar si hay function calls
            if response.candidates and response.candidates[0].function_calls:
                print(f"   🎉 FUNCTION CALL DETECTADO!")
                for fc in response.candidates[0].function_calls:
                    print(f"   Función: {fc.name}")
                    print(f"   Argumentos: {dict(fc.args)}")

                print(f"   ✅ Function calling funcionando correctamente")

            elif response.candidates and response.candidates[0].text:
                content = response.candidates[0].text
                print(f"   📝 Respuesta de texto: {content[:100]}...")

                if "El modelo está procesando" in content:
                    print(f"   ❌ Aún devuelve mensaje genérico")
                else:
                    print(f"   ✅ Respuesta de texto válida")
            else:
                print(f"   ❌ Respuesta inesperada: {response}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    # 3. Test con conversación más larga
    print(f"\n4. Probando conversación con historial")
    try:
        conversation = [
            Content(role="user", parts=[Part.from_text("Hola, soy un inversor interesado en Tesla")]),
            Content(role="model", parts=[Part.from_text("¡Hola! Te puedo ayudar con información sobre Tesla. ¿Qué te gustaría saber?")]),
            Content(role="user", parts=[Part.from_text("Necesito el precio actual y análisis técnico de NASDAQ:TSLA")])
        ]

        response = await generate_chat_response(conversation, model)

        print(f"   ✅ Respuesta de conversación recibida")
        print(f"   Tipo: {type(response)}")

        if response.candidates and response.candidates[0].function_calls:
            print(f"   🎉 FUNCTION CALL EN CONVERSACIÓN!")
            for fc in response.candidates[0].function_calls:
                print(f"   Función: {fc.name}")
                print(f"   Argumentos: {dict(fc.args)}")
        elif response.candidates and response.candidates[0].text:
            print(f"   📝 Respuesta: {response.candidates[0].text[:100]}...")

    except Exception as e:
        print(f"   ❌ Error en conversación: {e}")


async def main():
    """Ejecutar las pruebas."""
    await test_chat_endpoint()


if __name__ == "__main__":
    asyncio.run(main())
