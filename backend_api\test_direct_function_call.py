#!/usr/bin/env python3
"""
Test directo para verificar function calling con Gemini 2.5 Pro
"""

import asyncio
import json
from app.services.vertex_ai import initialize_gemini_model_with_dynamic_tools
from vertexai.generative_models import Content, Part, GenerationConfig, ToolConfig


async def test_direct_function_calling():
    """Test directo siguiendo exactamente la documentación de Google Cloud."""
    print("🔍 TESTING DIRECT FUNCTION CALLING")
    print("="*50)
    
    # 1. Inicializar modelo
    print("1. Inicializando modelo...")
    model = await initialize_gemini_model_with_dynamic_tools()
    
    if not model:
        print("❌ Error: No se pudo inicializar el modelo")
        return
    
    print(f"✅ Modelo inicializado: {model._model_name}")
    print(f"✅ Herramientas disponibles: {model._tools is not None}")
    
    if model._tools:
        print(f"✅ Número de grupos de herramientas: {len(model._tools)}")
        for i, tool_group in enumerate(model._tools):
            if hasattr(tool_group, 'function_declarations'):
                print(f"  Grupo {i+1}: {len(tool_group.function_declarations)} funciones")
                for func in tool_group.function_declarations:
                    print(f"    - {func.name}")
    
    # 2. Probar con diferentes configuraciones
    test_cases = [
        {
            "name": "Sin ToolConfig",
            "prompt": "¿Cuál es el precio actual de Tesla (NASDAQ:TSLA)?",
            "use_tool_config": False
        },
        {
            "name": "Con ToolConfig ANY",
            "prompt": "¿Cuál es el precio actual de Tesla (NASDAQ:TSLA)?",
            "use_tool_config": True
        },
        {
            "name": "Prompt muy específico",
            "prompt": "Usa la función get_asset_data_from_db con el parámetro symbol='NASDAQ:TSLA' para obtener el precio actual de Tesla",
            "use_tool_config": True
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Probando: {test_case['name']}")
        print(f"   Prompt: {test_case['prompt'][:50]}...")
        
        try:
            # Preparar contenido
            contents = [Content(role="user", parts=[Part.from_text(test_case['prompt'])])]
            
            # Configurar parámetros
            generation_config = GenerationConfig(temperature=0)
            
            kwargs = {
                "generation_config": generation_config
            }
            
            if test_case['use_tool_config']:
                tool_config = ToolConfig(
                    function_calling_config=ToolConfig.FunctionCallingConfig(
                        mode=ToolConfig.FunctionCallingConfig.Mode.ANY
                    )
                )
                kwargs["tool_config"] = tool_config
                print("   ✅ Usando ToolConfig con modo ANY")
            
            # Hacer la llamada
            response = model.generate_content(contents, **kwargs)
            
            print(f"   ✅ Respuesta recibida")
            print(f"   Candidatos: {len(response.candidates) if response.candidates else 0}")
            
            if response.candidates:
                candidate = response.candidates[0]
                print(f"   Finish reason: {candidate.finish_reason}")
                print(f"   Partes: {len(candidate.content.parts) if candidate.content and candidate.content.parts else 0}")
                
                # Verificar function calls
                function_calls_found = False
                
                # Método 1: Usar candidate.function_calls
                if hasattr(candidate, 'function_calls') and candidate.function_calls:
                    print(f"   🎉 FUNCTION CALLS ENCONTRADOS (método 1): {len(candidate.function_calls)}")
                    for fc in candidate.function_calls:
                        print(f"      - {fc.name}: {dict(fc.args)}")
                    function_calls_found = True
                
                # Método 2: Buscar en las partes
                if candidate.content and candidate.content.parts:
                    for j, part in enumerate(candidate.content.parts):
                        if hasattr(part, 'function_call') and part.function_call:
                            print(f"   🎉 FUNCTION CALL ENCONTRADO (método 2) en parte {j+1}:")
                            print(f"      - {part.function_call.name}: {dict(part.function_call.args)}")
                            function_calls_found = True
                        elif hasattr(part, 'text') and part.text:
                            print(f"   📝 Texto en parte {j+1}: {part.text[:100]}...")
                        else:
                            print(f"   ⚠️ Parte {j+1} tipo: {type(part)}")
                            part_str = str(part)
                            if 'thought_signature' in part_str:
                                print(f"      Contiene thought_signature")
                            else:
                                print(f"      Contenido: {part_str[:100]}...")
                
                if not function_calls_found:
                    print("   ❌ No se encontraron function calls")
            else:
                print("   ❌ No hay candidatos en la respuesta")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    # 3. Probar con un ejemplo exacto de la documentación
    print(f"\n4. Probando ejemplo exacto de la documentación de Google Cloud")
    try:
        from vertexai.generative_models import FunctionDeclaration, Tool
        
        # Crear una función simple como en la documentación
        get_weather_func = FunctionDeclaration(
            name="get_current_weather",
            description="Get the current weather in a given location",
            parameters={
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city name of the location for which to get the weather."
                    }
                },
                "required": ["location"]
            }
        )
        
        # Crear modelo con esta función específica
        from vertexai.generative_models import GenerativeModel
        import vertexai
        
        test_model = GenerativeModel(
            model_name="gemini-2.5-pro",
            tools=[Tool(function_declarations=[get_weather_func])]
        )
        
        # Probar con ToolConfig ANY
        tool_config = ToolConfig(
            function_calling_config=ToolConfig.FunctionCallingConfig(
                mode=ToolConfig.FunctionCallingConfig.Mode.ANY
            )
        )
        
        response = test_model.generate_content(
            [Content(role="user", parts=[Part.from_text("What is the weather like in Boston?")])],
            generation_config=GenerationConfig(temperature=0),
            tool_config=tool_config
        )
        
        print("   ✅ Respuesta del ejemplo de documentación recibida")
        
        if response.candidates:
            candidate = response.candidates[0]
            if hasattr(candidate, 'function_calls') and candidate.function_calls:
                print(f"   🎉 FUNCTION CALLS FUNCIONAN CON EJEMPLO DE DOCUMENTACIÓN!")
                for fc in candidate.function_calls:
                    print(f"      - {fc.name}: {dict(fc.args)}")
            else:
                print("   ❌ Ejemplo de documentación tampoco genera function calls")
                if candidate.content and candidate.content.parts:
                    for part in candidate.content.parts:
                        print(f"      Parte: {str(part)[:100]}...")
        
    except Exception as e:
        print(f"   ❌ Error con ejemplo de documentación: {e}")


async def main():
    """Ejecutar las pruebas."""
    await test_direct_function_calling()


if __name__ == "__main__":
    asyncio.run(main())
