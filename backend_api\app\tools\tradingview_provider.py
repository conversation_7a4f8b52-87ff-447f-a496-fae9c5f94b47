"""
TradingView data provider for TradingIA Backend.

This module provides financial data and technical analysis tools
using the tvDatafeed library. It acts as an abstraction layer
that can be easily replaced with other data providers if needed.
"""

# Primary data provider: yfinance (more reliable)
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    yf = None

# Fallback data provider: tvDatafeed
try:
    from tvDatafeed import TvDatafeed, Interval
    TVDATAFEED_AVAILABLE = True
except ImportError:
    TVDATAFEED_AVAILABLE = False
    # Mock for testing when tvDatafeed is not available
    class TvDatafeed:
        def get_hist(self, symbol, exchange, interval, n_bars):
            return None

    class Interval:
        in_1_minute = "1m"
        in_5_minute = "5m"
        in_15_minute = "15m"
        in_30_minute = "30m"
        in_1_hour = "1h"
        in_4_hour = "4h"
        in_daily = "1D"
        in_weekly = "1W"
        in_monthly = "1M"
from fastapi import HTTPException, status
from typing import Dict, Any, Optional
import pandas as pd
import logging
from datetime import datetime, timedelta

# Import pandas_ta for technical analysis
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    ta = None

# Configure logging
logger = logging.getLogger(__name__)

# Initialize TvDatafeed client (fallback)
if TVDATAFEED_AVAILABLE:
    tv = TvDatafeed()
else:
    tv = None

# Interval mapping for tvDatafeed
INTERVAL_MAP = {
    "1m": Interval.in_1_minute,
    "5m": Interval.in_5_minute,
    "15m": Interval.in_15_minute,
    "30m": Interval.in_30_minute,
    "1h": Interval.in_1_hour,
    "4h": Interval.in_4_hour,
    "1D": Interval.in_daily,
    "1W": Interval.in_weekly,
    "1M": Interval.in_monthly
}



# Interval mapping for yfinance
YFINANCE_INTERVAL_MAP = {
    "1m": "1m",
    "5m": "5m",
    "15m": "15m",
    "30m": "30m",
    "1h": "1h",
    "4h": "4h",
    "1D": "1d",
    "1W": "1wk",
    "1M": "1mo"
}

def get_data_with_yfinance(symbol: str, interval: str, n_bars: int) -> pd.DataFrame:
    """
    Get price data using yfinance (primary provider).

    Args:
        symbol: Trading symbol (e.g., "NASDAQ:TSLA" or "TSLA")
        interval: Time interval
        n_bars: Number of bars to retrieve

    Returns:
        DataFrame with OHLCV data
    """
    try:
        # Parse symbol - remove exchange prefix for yfinance
        if ":" in symbol:
            exchange, ticker = symbol.split(":", 1)
        else:
            ticker = symbol

        # Map crypto and other symbols for yfinance
        symbol_mapping = {
            # Crypto pairs
            "BTCUSDT": "BTC-USD",
            "ETHUSDT": "ETH-USD",
            "BNBUSDT": "BNB-USD",
            "ADAUSDT": "ADA-USD",
            "SOLUSDT": "SOL-USD",
            "XRPUSDT": "XRP-USD",
            "DOTUSDT": "DOT-USD",
            "AVAXUSDT": "AVAX-USD",
            "LINKUSDT": "LINK-USD",
            "MATICUSDT": "MATIC-USD",
            "UNIUSDT": "UNI-USD",
            "LTCUSDT": "LTC-USD",
            "BCHUSDT": "BCH-USD",
            "XLMUSDT": "XLM-USD",
            "VETUSDT": "VET-USD",
            "TRXUSDT": "TRX-USD",
            "XMRUSDT": "XMR-USD",
            "DASHUSDT": "DASH-USD",
            # Forex pairs
            "EURUSD": "EURUSD=X",
            "GBPUSD": "GBPUSD=X",
            "USDJPY": "USDJPY=X",
            "USDCHF": "USDCHF=X",
            "AUDUSD": "AUDUSD=X",
            "USDCAD": "USDCAD=X",
            # Commodities
            "GC1!": "GC=F",  # Gold futures
            "SI1!": "SI=F",  # Silver futures
            "CL1!": "CL=F",  # Crude oil futures
            "ZC1!": "ZC=F",  # Corn futures
            "ZW1!": "ZW=F"   # Wheat futures
        }

        yf_symbol = symbol_mapping.get(ticker, ticker)

        # Get yfinance interval
        yf_interval = YFINANCE_INTERVAL_MAP.get(interval, "1d")

        # Calculate period based on n_bars and interval
        if interval in ["1m", "5m", "15m", "30m"]:
            period = "1d"  # Intraday data
        elif interval in ["1h", "4h"]:
            period = "1mo"  # CAMBIO: Aumentar de "5d" a "1mo" (1 mes)
        elif interval == "1D":
            if n_bars <= 5:
                period = "5d"
            elif n_bars <= 30:
                period = "1mo"
            elif n_bars <= 90:
                period = "3mo"
            else:
                period = "1y"
        else:
            period = "1y"

        # Get data from yfinance
        ticker_obj = yf.Ticker(yf_symbol)
        data = ticker_obj.history(period=period, interval=yf_interval)

        if data.empty:
            return None

        # Limit to requested number of bars
        if len(data) > n_bars:
            data = data.tail(n_bars)

        # Rename columns to match expected format
        data.columns = [col.lower() for col in data.columns]

        return data

    except Exception as e:
        logger.error(f"Error getting data with yfinance: {e}")
        return None


def get_price_data(symbol: str, interval: str = "1D", n_bars: int = 100) -> Dict[str, Any]:
    """
    Obtain historical OHLCV price data for any asset.
    
    This function uses tvDatafeed to retrieve historical price data
    and formats it in a clean, consistent structure for the AI model.
    
    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA", "BINANCE:BTCUSDT")
        interval (str): Time interval ("1m", "5m", "15m", "30m", "1h", "4h", "1D", "1W", "1M")
        n_bars (int): Number of bars to retrieve (max 5000)
        
    Returns:
        Dict[str, Any]: Formatted price data with OHLCV information
        
    Raises:
        HTTPException: If data retrieval fails or symbol not found
    """
    try:
        # Validate inputs
        if interval not in INTERVAL_MAP:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid interval. Supported intervals: {list(INTERVAL_MAP.keys())}"
            )
        
        if n_bars <= 0 or n_bars > 5000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="n_bars must be between 1 and 5000"
            )
        
        # Parse symbol (format: "EXCHANGE:SYMBOL")
        if ":" in symbol:
            exchange, ticker = symbol.split(":", 1)
        else:
            # Default to NASDAQ if no exchange specified
            exchange = "NASDAQ"
            ticker = symbol
        
        logger.info(f"Fetching price data for {exchange}:{ticker}, interval: {interval}, bars: {n_bars}")

        # Try yfinance first (primary provider)
        data = None
        if YFINANCE_AVAILABLE:
            logger.info(f"Trying yfinance for {exchange}:{ticker}")
            data = get_data_with_yfinance(f"{exchange}:{ticker}", interval, n_bars)

        # Fallback to tvDatafeed if yfinance fails
        if data is None and TVDATAFEED_AVAILABLE and tv is not None:
            logger.info(f"Falling back to tvDatafeed for {exchange}:{ticker}")
            try:
                data = tv.get_hist(
                    symbol=ticker,
                    exchange=exchange,
                    interval=INTERVAL_MAP[interval],
                    n_bars=n_bars
                )
            except Exception as e:
                logger.warning(f"tvDatafeed also failed: {e}")
                data = None

        # If no data and it's a problematic exchange, try NASDAQ fallback
        if (data is None or data.empty) and exchange in ["NYSE", "FOREX"]:
            logger.warning(f"No data for {exchange}:{ticker}, trying NASDAQ fallback...")
            if exchange == "NYSE" and ticker in ["SPY", "QQQ", "IWM"]:
                # Try NASDAQ equivalent for ETFs
                fallback_ticker = "QQQ" if ticker == "SPY" else ticker
                data = tv.get_hist(
                    symbol=fallback_ticker,
                    exchange="NASDAQ",
                    interval=INTERVAL_MAP[interval],
                    n_bars=n_bars
                )
                if data is not None and not data.empty:
                    logger.info(f"Fallback successful: NASDAQ:{fallback_ticker}")
                    exchange = "NASDAQ"
                    ticker = fallback_ticker

        if data is None or data.empty:
            logger.warning(f"No data found for symbol: {exchange}:{ticker}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol: {exchange}:{ticker}"
            )
        
        # Format the data
        formatted_data = {
            "symbol": f"{exchange}:{ticker}",
            "interval": interval,
            "bars_count": len(data),
            "data": []
        }
        
        # Convert DataFrame to list of dictionaries
        for index, row in data.iterrows():
            bar_data = {
                "datetime": index.isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": int(row['volume']) if pd.notna(row['volume']) else 0
            }
            formatted_data["data"].append(bar_data)
        
        # Add summary statistics
        latest_bar = formatted_data["data"][-1] if formatted_data["data"] else None
        if latest_bar:
            formatted_data["latest_price"] = latest_bar["close"]
            formatted_data["latest_datetime"] = latest_bar["datetime"]
        
        logger.info(f"Successfully retrieved {len(formatted_data['data'])} bars for {exchange}:{ticker}")
        return formatted_data
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error fetching price data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch price data: {str(e)}"
        )


def apply_indicator(
    symbol: str,
    interval: str,
    indicator_name: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Calculate and return technical indicator values.

    This function first obtains price data and then applies the specified
    technical indicator calculation over the data.

    Args:
        symbol (str): Trading symbol (e.g., "NASDAQ:TSLA")
        interval (str): Time interval for the data
        indicator_name (str): Name of the indicator (e.g., "RSI", "MACD", "SMA")
        parameters (Dict[str, Any]): Indicator parameters (e.g., {"length": 14})

    Returns:
        Dict[str, Any]: Indicator values in structured format

    Raises:
        HTTPException: If calculation fails or indicator not supported
    """
    try:
        # Check if pandas_ta is available
        if not PANDAS_TA_AVAILABLE:
            logger.error("pandas_ta library not available")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Technical analysis library not available"
            )

        # Supported indicators
        SUPPORTED_INDICATORS = {
            # Existing indicators
            "RSI": "rsi",
            "MACD": "macd",
            "SMA": "sma",
            "EMA": "ema",
            "BBANDS": "bbands",
            "STOCH": "stoch",
            "ATR": "atr",
            "ADX": "adx",
            # New volume indicators
            "OBV": "obv",
            "MFI": "mfi",
            # New momentum indicators
            "CCI": "cci",
            "WILLIAMS_R": "willr",
            "MOMENTUM": "mom",
            # New volatility indicators
            "KELTNER": "kc",
            "DONCHIAN": "donchian"
        }

        if indicator_name.upper() not in SUPPORTED_INDICATORS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported indicator. Supported indicators: {list(SUPPORTED_INDICATORS.keys())}"
            )

        # Get price data (default to 100 bars for indicator calculation)
        n_bars = parameters.get("n_bars", 100)
        price_data = get_price_data(symbol, interval, n_bars)

        # Convert to DataFrame for pandas_ta with validation
        df_data = []
        skipped_bars = 0

        for bar in price_data["data"]:
            # Validar que todos los valores sean válidos
            open_val = bar.get("open")
            high_val = bar.get("high")
            low_val = bar.get("low")
            close_val = bar.get("close")
            volume_val = bar.get("volume")

            # Saltar barras con valores None o inválidos
            if any(val is None for val in [open_val, high_val, low_val, close_val]):
                skipped_bars += 1
                continue

            # Validar que sean números válidos
            try:
                open_float = float(open_val)
                high_float = float(high_val)
                low_float = float(low_val)
                close_float = float(close_val)
                volume_float = float(volume_val) if volume_val is not None else 0.0

                # Validar relaciones OHLC básicas
                if high_float < max(open_float, close_float) or low_float > min(open_float, close_float):
                    skipped_bars += 1
                    continue

                df_data.append({
                    "open": open_float,
                    "high": high_float,
                    "low": low_float,
                    "close": close_float,
                    "volume": volume_float
                })

            except (ValueError, TypeError) as e:
                skipped_bars += 1
                continue

        # Verificar que tengamos suficientes datos válidos
        if len(df_data) < 20:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient valid data for indicator calculation. Got {len(df_data)} bars, need at least 20. Skipped {skipped_bars} invalid bars."
            )

        if skipped_bars > 0:
            logger.warning(f"Skipped {skipped_bars} invalid bars out of {len(price_data['data'])} total bars for {symbol}")

        df = pd.DataFrame(df_data)

        # Apply the indicator
        indicator_func = SUPPORTED_INDICATORS[indicator_name.upper()]

        if indicator_name.upper() == "RSI":
            length = parameters.get("length", 14)
            result = ta.rsi(df["close"], length=length)

        elif indicator_name.upper() == "MACD":
            fast = parameters.get("fast", 12)
            slow = parameters.get("slow", 26)
            signal = parameters.get("signal", 9)
            result = ta.macd(df["close"], fast=fast, slow=slow, signal=signal)

        elif indicator_name.upper() == "SMA":
            length = parameters.get("length", 20)
            result = ta.sma(df["close"], length=length)

        elif indicator_name.upper() == "EMA":
            length = parameters.get("length", 20)
            result = ta.ema(df["close"], length=length)

        elif indicator_name.upper() == "BBANDS":
            length = parameters.get("length", 20)
            std = parameters.get("std", 2)
            result = ta.bbands(df["close"], length=length, std=std)

        elif indicator_name.upper() == "STOCH":
            k = parameters.get("k", 14)
            d = parameters.get("d", 3)
            result = ta.stoch(df["high"], df["low"], df["close"], k=k, d=d)

        elif indicator_name.upper() == "ATR":
            length = parameters.get("length", 14)
            result = ta.atr(df["high"], df["low"], df["close"], length=length)

        elif indicator_name.upper() == "ADX":
            length = parameters.get("length", 14)
            result = ta.adx(df["high"], df["low"], df["close"], length=length)

        elif indicator_name.upper() == "OBV":
            result = ta.obv(df["close"], df["volume"])

        elif indicator_name.upper() == "MFI":
            length = parameters.get("length", 14)
            result = ta.mfi(df["high"], df["low"], df["close"], df["volume"], length=length)

        elif indicator_name.upper() == "CCI":
            length = parameters.get("length", 20)
            result = ta.cci(df["high"], df["low"], df["close"], length=length)

        elif indicator_name.upper() == "WILLIAMS_R":
            length = parameters.get("length", 14)
            result = ta.willr(df["high"], df["low"], df["close"], length=length)

        elif indicator_name.upper() == "MOMENTUM":
            length = parameters.get("length", 10)
            result = ta.mom(df["close"], length=length)

        elif indicator_name.upper() == "KELTNER":
            length = parameters.get("length", 20)
            scalar = parameters.get("scalar", 2)
            result = ta.kc(df["high"], df["low"], df["close"], length=length, scalar=scalar)

        elif indicator_name.upper() == "DONCHIAN":
            length = parameters.get("length", 20)
            result = ta.donchian(df["high"], df["low"], lower_length=length, upper_length=length)

        # Format the result
        formatted_result = {
            "symbol": symbol,
            "interval": interval,
            "indicator": indicator_name.upper(),
            "parameters": parameters,
            "values": []
        }

        # Handle different result types
        if isinstance(result, pd.Series):
            # Single series (RSI, SMA, EMA, ATR)
            for i, value in enumerate(result):
                if pd.notna(value):
                    formatted_result["values"].append({
                        "index": i,
                        "datetime": price_data["data"][i]["datetime"],
                        "value": float(value)
                    })

        elif isinstance(result, pd.DataFrame):
            # Multiple series (MACD, BBANDS, STOCH, ADX)
            for i in range(len(result)):
                row_data = {"index": i, "datetime": price_data["data"][i]["datetime"]}
                for col in result.columns:
                    if pd.notna(result.iloc[i][col]):
                        row_data[col.lower()] = float(result.iloc[i][col])
                if len(row_data) > 2:  # More than just index and datetime
                    formatted_result["values"].append(row_data)

        # Add latest value summary
        if formatted_result["values"]:
            latest = formatted_result["values"][-1]
            formatted_result["latest_value"] = latest

        logger.info(f"Successfully calculated {indicator_name} for {symbol}")
        return formatted_result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error calculating indicator: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate indicator: {str(e)}"
        )
