"""
Unit tests for Supabase tools.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

from app.tools.supabase_tools import (
    get_asset_data_from_db,
    find_assets_by_criteria,
    get_market_summary,
    get_technical_analysis_summary,
    get_supabase_tools
)


class TestGetAssetDataFromDB:
    """Test cases for get_asset_data_from_db function."""
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_get_asset_data_complete(self, mock_get_client):
        """Test getting complete asset data from all tables."""
        # Mock Supabase client
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock responses for all tables
        mock_client.table.return_value.select.return_value.eq.return_value.execute.side_effect = [
            # market_data
            MagicMock(data=[{
                "symbol": "NASDAQ:TSLA",
                "current_price": 250.0,
                "change_24h": 5.0,
                "change_percent_24h": 2.0
            }]),
            # technical_indicators
            MagicMock(data=[
                {"indicator_name": "RSI", "value": 65.0, "interpretation": "Neutral", "calculated_at": "2025-01-01T00:00:00"},
                {"indicator_name": "MACD", "value": 1.5, "interpretation": "Bullish", "calculated_at": "2025-01-01T00:00:00"}
            ]),
            # volume_analysis
            MagicMock(data=[{
                "volume_trend": "increasing",
                "volume_confirmation": True,
                "vwap_value": 248.0
            }]),
            # pattern_analysis
            MagicMock(data=[{
                "pattern_type": "bullish_flag",
                "confidence_score": 85.0,
                "target_price": 280.0
            }]),
            # multi_timeframe_analysis
            MagicMock(data=[{
                "confluence_direction": "bullish",
                "confluence_percentage": 75.0,
                "confluence_strength": "strong"
            }]),
            # support_resistance_levels
            MagicMock(data=[
                {"level_type": "SUPPORT", "price_level": 240.0, "strength": 8},
                {"level_type": "RESISTANCE", "price_level": 260.0, "strength": 7}
            ]),
            # market_structure_analysis
            MagicMock(data=[{
                "trend_structure": "bullish",
                "market_phase": "trending",
                "overall_bias": "bullish"
            }])
        ]
        
        result = await get_asset_data_from_db("NASDAQ:TSLA")
        
        # Assertions
        assert result["symbol"] == "NASDAQ:TSLA"
        assert "timestamp" in result
        assert len(result["data_sources"]) == 7
        assert "market_data" in result
        assert "technical_indicators" in result
        assert "volume_analysis" in result
        assert "pattern_analysis" in result
        assert "multi_timeframe_analysis" in result
        assert "support_resistance_levels" in result
        assert "market_structure_analysis" in result
        
        # Check technical indicators structure
        assert "RSI" in result["technical_indicators"]
        assert result["technical_indicators"]["RSI"]["value"] == 65.0
        assert result["technical_indicators"]["RSI"]["interpretation"] == "Neutral"
        
        # Check support/resistance structure
        assert len(result["support_resistance_levels"]["support"]) == 1
        assert len(result["support_resistance_levels"]["resistance"]) == 1
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_get_asset_data_no_data(self, mock_get_client):
        """Test getting asset data when no data exists."""
        # Mock Supabase client with empty responses
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = MagicMock(data=[])
        
        result = await get_asset_data_from_db("NASDAQ:UNKNOWN")
        
        assert result["symbol"] == "NASDAQ:UNKNOWN"
        assert len(result["data_sources"]) == 0
        assert "market_data" not in result
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_get_asset_data_error(self, mock_get_client):
        """Test error handling in get_asset_data_from_db."""
        # Mock Supabase client to raise exception
        mock_get_client.side_effect = Exception("Database connection failed")
        
        result = await get_asset_data_from_db("NASDAQ:TSLA")
        
        assert "error" in result
        assert "Database connection failed" in result["error"]


class TestFindAssetsByCriteria:
    """Test cases for find_assets_by_criteria function."""
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_find_by_rsi_oversold(self, mock_get_client):
        """Test finding assets by RSI oversold condition."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock query chain
        mock_query = MagicMock()
        mock_client.table.return_value.select.return_value.eq.return_value = mock_query
        mock_query.lt.return_value.execute.return_value = MagicMock(data=[
            {"symbol": "NASDAQ:TSLA"},
            {"symbol": "NASDAQ:AAPL"}
        ])
        
        criteria = {"indicator": "RSI", "condition": "oversold", "value": 30}
        result = await find_assets_by_criteria(criteria)
        
        assert len(result) == 2
        assert "NASDAQ:TSLA" in result
        assert "NASDAQ:AAPL" in result
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_find_by_pattern(self, mock_get_client):
        """Test finding assets by pattern."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        # Mock query chain
        mock_query = MagicMock()
        mock_client.table.return_value.select.return_value.eq.return_value = mock_query
        mock_query.gte.return_value.execute.return_value = MagicMock(data=[
            {"symbol": "NASDAQ:MSFT"}
        ])
        
        criteria = {"pattern": "bullish_flag", "confidence": 80}
        result = await find_assets_by_criteria(criteria)
        
        assert len(result) == 1
        assert "NASDAQ:MSFT" in result
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_find_by_volume_confirmation(self, mock_get_client):
        """Test finding assets by volume confirmation."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = MagicMock(data=[
            {"symbol": "NASDAQ:GOOGL"}
        ])
        
        criteria = {"volume_confirmation": True}
        result = await find_assets_by_criteria(criteria)
        
        assert len(result) == 1
        assert "NASDAQ:GOOGL" in result


class TestGetMarketSummary:
    """Test cases for get_market_summary function."""
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_get_market_summary_all(self, mock_get_client):
        """Test getting market summary for all categories."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_client.table.return_value.select.return_value.execute.return_value = MagicMock(data=[
            {"symbol": "NASDAQ:TSLA", "name": "Tesla", "current_price": 250.0, "change_percent_24h": 2.0},
            {"symbol": "NASDAQ:AAPL", "name": "Apple", "current_price": 180.0, "change_percent_24h": -1.0},
            {"symbol": "BINANCE:BTCUSDT", "name": "Bitcoin", "current_price": 45000.0, "change_percent_24h": 3.0}
        ])
        
        result = await get_market_summary("all")
        
        assert result["category"] == "all"
        assert result["total_assets"] == 3
        assert result["gainers_count"] == 2
        assert result["losers_count"] == 1
        assert len(result["top_gainers"]) <= 5
        assert len(result["top_losers"]) <= 5
    
    @patch('app.tools.supabase_tools.get_supabase_client')
    @pytest.mark.asyncio
    async def test_get_market_summary_tech(self, mock_get_client):
        """Test getting market summary for tech category."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        mock_client.table.return_value.select.return_value.execute.return_value = MagicMock(data=[
            {"symbol": "NASDAQ:TSLA", "name": "Tesla", "current_price": 250.0, "change_percent_24h": 2.0},
            {"symbol": "NASDAQ:AAPL", "name": "Apple", "current_price": 180.0, "change_percent_24h": -1.0},
            {"symbol": "BINANCE:BTCUSDT", "name": "Bitcoin", "current_price": 45000.0, "change_percent_24h": 3.0}
        ])
        
        result = await get_market_summary("tech")
        
        assert result["category"] == "tech"
        # Should filter to only NASDAQ/NYSE symbols
        assert result["total_assets"] == 2


class TestGetTechnicalAnalysisSummary:
    """Test cases for get_technical_analysis_summary function."""
    
    @patch('app.tools.supabase_tools.get_asset_data_from_db')
    @pytest.mark.asyncio
    async def test_technical_analysis_summary(self, mock_get_asset_data):
        """Test getting technical analysis summary."""
        # Mock asset data
        mock_get_asset_data.return_value = {
            "symbol": "NASDAQ:TSLA",
            "technical_indicators": {
                "RSI": {"value": 65.0, "interpretation": "neutral"},
                "MACD": {"value": 1.5, "interpretation": "bullish"}
            },
            "support_resistance_levels": {
                "support": [{"price_level": 240.0, "strength": 8}],
                "resistance": [{"price_level": 260.0, "strength": 7}]
            },
            "pattern_analysis": [
                {"pattern_type": "bullish_flag", "confidence_score": 85.0, "target_price": 280.0}
            ],
            "volume_analysis": {
                "volume_trend": "increasing",
                "volume_confirmation": True,
                "vwap_value": 248.0
            }
        }
        
        result = await get_technical_analysis_summary("NASDAQ:TSLA")
        
        assert result["symbol"] == "NASDAQ:TSLA"
        assert "overall_sentiment" in result
        assert "confidence_score" in result
        assert "indicators_summary" in result
        assert "key_levels" in result
        assert "patterns_detected" in result
        assert "volume_analysis" in result
        
        # Check indicators summary
        assert result["indicators_summary"]["total_indicators"] == 2
        assert result["indicators_summary"]["bullish_signals"] == 1
        assert result["indicators_summary"]["neutral_signals"] == 1


class TestGetSupabaseTools:
    """Test cases for get_supabase_tools function."""
    
    def test_get_supabase_tools(self):
        """Test that get_supabase_tools returns correct tool definitions."""
        tools = get_supabase_tools()
        
        assert isinstance(tools, list)
        assert len(tools) == 4
        
        tool_names = [tool["name"] for tool in tools]
        assert "get_asset_data_from_db" in tool_names
        assert "find_assets_by_criteria" in tool_names
        assert "get_market_summary" in tool_names
        assert "get_technical_analysis_summary" in tool_names
        
        # Check tool structure
        for tool in tools:
            assert "name" in tool
            assert "description" in tool
            assert "parameters" in tool
            assert "type" in tool["parameters"]
            assert "properties" in tool["parameters"]
