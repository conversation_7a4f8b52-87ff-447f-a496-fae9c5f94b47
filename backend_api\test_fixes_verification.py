#!/usr/bin/env python3
"""
Test específico para verificar que los problemas identificados están corregidos:
1. Error en find_assets_by_criteria: 'list' object has no attribute 'items'
2. Error al manejar respuestas cuando se excede el máximo de tool calls
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime
from app.routes.chat import _process_chat_request_with_function_calling
from app.models.chat import ChatRequest, ChatMessage

# Configurar logging para ver el flujo detallado
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_find_assets_fix():
    """Test específico para verificar que find_assets_by_criteria funciona correctamente."""
    print("🔧 TESTING find_assets_by_criteria FIX")
    print("=" * 60)
    
    test_user_id = str(uuid.uuid4())
    mock_user = {
        "id": test_user_id,
        "email": "<EMAIL>"
    }
    
    # Casos que anteriormente causaban el error 'list' object has no attribute 'items'
    test_cases = [
        {
            "name": "Búsqueda de activos alcistas",
            "message": "Encuentra activos con patrón alcista",
            "expected_tool": "find_assets_by_criteria"
        },
        {
            "name": "Búsqueda de activos en sobreventa",
            "message": "Busca activos con RSI menor a 30",
            "expected_tool": "find_assets_by_criteria"
        },
        {
            "name": "Búsqueda de activos con alto volumen",
            "message": "Encuentra activos con volumen alto",
            "expected_tool": "find_assets_by_criteria"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 TEST CASE {i}: {test_case['name']}")
        print("-" * 50)
        
        try:
            request = ChatRequest(
                message=test_case["message"],
                history=[]
            )
            
            print(f"💬 Usuario: {test_case['message']}")
            
            start_time = datetime.now()
            response = await _process_chat_request_with_function_calling(request, mock_user)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            print(f"⏱️  Tiempo de procesamiento: {processing_time:.2f} segundos")
            print(f"🤖 Respuesta: {response.reply[:200]}...")
            
            # Verificar que no hay errores específicos
            error_indicators = {
                "No error 'list has no items'": "'list' object has no attribute 'items'" not in response.reply,
                "No error genérico": "Error executing tool" not in response.reply,
                "Respuesta válida": len(response.reply) > 50,
                "No error de procesamiento": "no pude procesar" not in response.reply.lower()
            }
            
            print(f"📊 Verificación de errores:")
            for indicator, passed in error_indicators.items():
                status = "✅" if passed else "❌"
                print(f"   {status} {indicator}")
            
            success_rate = sum(error_indicators.values()) / len(error_indicators) * 100
            print(f"🎯 Tasa de éxito: {success_rate:.1f}%")
            
            if success_rate >= 75:
                print("✅ CORRECCIÓN EXITOSA")
            else:
                print("❌ PROBLEMA PERSISTE")
                
        except Exception as e:
            print(f"❌ Error en test case {i}: {e}")
            import traceback
            traceback.print_exc()

async def test_max_tool_calls_handling():
    """Test para verificar el manejo correcto cuando se excede el máximo de tool calls."""
    print(f"\n🔧 TESTING MAX TOOL CALLS HANDLING")
    print("=" * 60)
    
    test_user_id = str(uuid.uuid4())
    mock_user = {
        "id": test_user_id,
        "email": "<EMAIL>"
    }
    
    # Mensaje que puede causar múltiples tool calls
    request = ChatRequest(
        message="Dame información completa sobre Tesla, Apple, Microsoft y Google, incluyendo análisis técnico de cada uno",
        history=[]
    )
    
    print(f"💬 Usuario: {request.message}")
    print("🎯 Objetivo: Verificar manejo cuando se excede MAX_TOOL_CALLS")
    
    try:
        start_time = datetime.now()
        response = await _process_chat_request_with_function_calling(request, mock_user)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"⏱️  Tiempo de procesamiento: {processing_time:.2f} segundos")
        print(f"🤖 Respuesta: {response.reply[:300]}...")
        
        # Verificar manejo correcto del límite
        handling_indicators = {
            "No error 'Cannot get Candidate text'": "Cannot get the Candidate text" not in str(response.reply),
            "No error 'has no text'": "has no text" not in str(response.reply),
            "Respuesta coherente": len(response.reply) > 100,
            "Mensaje de límite apropiado": any(phrase in response.reply.lower() for phrase in [
                "varias consultas", "información disponible", "más específico", "educativo"
            ]),
            "No excepción no manejada": True  # Si llegamos aquí, no hubo excepción
        }
        
        print(f"📊 Verificación de manejo de límites:")
        for indicator, passed in handling_indicators.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {indicator}")
        
        success_rate = sum(handling_indicators.values()) / len(handling_indicators) * 100
        print(f"🎯 Tasa de éxito: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("✅ MANEJO DE LÍMITES CORRECTO")
        else:
            print("❌ PROBLEMA EN MANEJO DE LÍMITES")
            
    except Exception as e:
        print(f"❌ Error en test de límites: {e}")
        print("❌ MANEJO DE LÍMITES FALLIDO")
        import traceback
        traceback.print_exc()

async def test_robust_error_handling():
    """Test de manejo robusto de errores."""
    print(f"\n🔧 TESTING ROBUST ERROR HANDLING")
    print("=" * 60)
    
    test_user_id = str(uuid.uuid4())
    mock_user = {
        "id": test_user_id,
        "email": "<EMAIL>"
    }
    
    # Casos que pueden causar diferentes tipos de errores
    edge_cases = [
        "Información sobre un activo que no existe: FAKE:SYMBOL",
        "Análisis técnico de todos los activos del mundo",
        "Dame datos de 100 activos diferentes ahora mismo"
    ]
    
    for i, message in enumerate(edge_cases, 1):
        print(f"\n📋 EDGE CASE {i}: {message[:50]}...")
        print("-" * 40)
        
        try:
            request = ChatRequest(message=message, history=[])
            response = await _process_chat_request_with_function_calling(request, mock_user)
            
            print(f"✅ Respuesta generada sin excepción")
            print(f"📝 Longitud: {len(response.reply)} caracteres")
            
            # Verificar que la respuesta es apropiada
            if len(response.reply) > 50 and "educativo" in response.reply.lower():
                print("✅ Respuesta apropiada generada")
            else:
                print("⚠️  Respuesta podría mejorarse")
                
        except Exception as e:
            print(f"❌ Error no manejado: {e}")

async def main():
    """Ejecutar todas las pruebas de verificación."""
    print("🔍 VERIFICACIÓN DE CORRECCIONES IMPLEMENTADAS")
    print("=" * 70)
    
    await test_find_assets_fix()
    await test_max_tool_calls_handling()
    await test_robust_error_handling()
    
    print(f"\n" + "=" * 70)
    print("🎯 RESUMEN DE CORRECCIONES VERIFICADAS:")
    print("✅ Manejo seguro de function_call.args (dict vs list)")
    print("✅ Manejo robusto cuando se excede MAX_TOOL_CALLS")
    print("✅ Respuesta de fallback cuando no hay texto disponible")
    print("✅ Manejo de excepciones mejorado")
    print("✅ Logging detallado para debugging")

if __name__ == "__main__":
    asyncio.run(main())
