#!/usr/bin/env python3
"""
Debug script to diagnose tool configuration issues.
"""

import asyncio
import json
from app.services.vertex_ai import initialize_gemini_model_with_dynamic_tools
from app.tools.supabase_tools import get_supabase_tools
from vertexai.generative_models import Content, Part


async def debug_tool_configuration():
    """Debug the tool configuration step by step."""
    print("🔍 DEBUGGING TOOL CONFIGURATION")
    print("="*50)
    
    # Step 1: Check tools format
    print("\n1. Checking Supabase tools format...")
    tools = get_supabase_tools()
    print(f"Number of tools: {len(tools)}")
    
    for i, tool in enumerate(tools):
        print(f"\nTool {i+1}: {tool['name']}")
        print(f"  Description: {tool['description'][:50]}...")
        print(f"  Parameters type: {tool['parameters']['type']}")
        print(f"  Required fields: {tool['parameters'].get('required', [])}")
        print(f"  Properties count: {len(tool['parameters'].get('properties', {}))}")
    
    # Step 2: Initialize model and check tools
    print("\n2. Initializing model with tools...")
    model = await initialize_gemini_model_with_dynamic_tools()
    
    if not model:
        print("❌ Model initialization failed")
        return
    
    print(f"✅ Model initialized: {type(model)}")
    
    # Step 3: Check model configuration
    print("\n3. Checking model configuration...")
    print(f"Model name: {model._model_name}")
    print(f"Has tools: {hasattr(model, '_tools') and model._tools is not None}")
    
    if hasattr(model, '_tools') and model._tools:
        print(f"Number of tool groups: {len(model._tools)}")
        for i, tool_group in enumerate(model._tools):
            print(f"  Tool group {i+1}: {type(tool_group)}")
            if hasattr(tool_group, 'function_declarations'):
                print(f"    Function declarations: {len(tool_group.function_declarations)}")
                for j, func_decl in enumerate(tool_group.function_declarations):
                    print(f"      Function {j+1}: {func_decl.name}")
    
    # Step 4: Test simple conversation
    print("\n4. Testing simple conversation...")
    try:
        contents = [
            Content(role="user", parts=[Part.from_text("Hola, ¿puedes ayudarme?")])
        ]
        
        response = model.generate_content(contents)
        print(f"✅ Simple conversation works")
        
        if response and response.candidates:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                first_part = candidate.content.parts[0]
                print(f"Response type: {type(first_part)}")
                
                if hasattr(first_part, 'text'):
                    print(f"Has text: {bool(first_part.text)}")
                if hasattr(first_part, 'function_call'):
                    print(f"Has function_call: {bool(first_part.function_call)}")
                
                # Check for thought_signature
                part_str = str(first_part)
                if 'thought_signature' in part_str:
                    print("⚠️ Found thought_signature in response")
                    print(f"Part string preview: {part_str[:200]}...")
                else:
                    print("✅ No thought_signature found")
                    
    except Exception as e:
        print(f"❌ Simple conversation failed: {e}")
    
    # Step 5: Test tool-triggering conversation
    print("\n5. Testing tool-triggering conversation...")
    try:
        contents = [
            Content(role="user", parts=[Part.from_text(
                "Necesito que uses la herramienta get_asset_data_from_db para obtener datos de NASDAQ:TSLA"
            )])
        ]
        
        response = model.generate_content(contents)
        print(f"✅ Tool-triggering conversation executed")
        
        if response and response.candidates:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                first_part = candidate.content.parts[0]
                
                print(f"Response part type: {type(first_part)}")
                
                if hasattr(first_part, 'function_call') and first_part.function_call:
                    print("✅ FUNCTION CALL DETECTED!")
                    print(f"Function name: {first_part.function_call.name}")
                    print(f"Function args: {dict(first_part.function_call.args)}")
                elif hasattr(first_part, 'text') and first_part.text:
                    print("⚠️ Got text response instead of function call")
                    print(f"Text: {first_part.text[:100]}...")
                
                # Check for thought_signature again
                part_str = str(first_part)
                if 'thought_signature' in part_str:
                    print("⚠️ Found thought_signature in tool-triggering response")
                    print(f"Part string preview: {part_str[:200]}...")
                else:
                    print("✅ No thought_signature in tool-triggering response")
                    
    except Exception as e:
        print(f"❌ Tool-triggering conversation failed: {e}")
    
    # Step 6: Test with explicit tool instruction
    print("\n6. Testing with explicit tool instruction...")
    try:
        contents = [
            Content(role="user", parts=[Part.from_text(
                "IMPORTANTE: Debes usar la función get_asset_data_from_db con el parámetro symbol='NASDAQ:TSLA'. No respondas con texto, usa la función."
            )])
        ]
        
        response = model.generate_content(contents)
        print(f"✅ Explicit tool instruction executed")
        
        if response and response.candidates:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                first_part = candidate.content.parts[0]
                
                if hasattr(first_part, 'function_call') and first_part.function_call:
                    print("🎉 SUCCESS! Function call with explicit instruction!")
                    print(f"Function name: {first_part.function_call.name}")
                    print(f"Function args: {dict(first_part.function_call.args)}")
                else:
                    print("❌ Still no function call even with explicit instruction")
                    if hasattr(first_part, 'text'):
                        print(f"Got text: {first_part.text[:100]}...")
                        
    except Exception as e:
        print(f"❌ Explicit tool instruction failed: {e}")


async def main():
    """Run the debug analysis."""
    await debug_tool_configuration()


if __name__ == "__main__":
    asyncio.run(main())
